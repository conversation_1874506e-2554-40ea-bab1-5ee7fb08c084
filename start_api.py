#!/usr/bin/env python3
"""
启动FastAPI服务的脚本
"""

import argparse
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import uvicorn


def main():
    """启动FastAPI服务"""
    parser = argparse.ArgumentParser(description='启动消息分析API服务')
    parser.add_argument('--host', type=str, default='0.0.0.0',
                       help='服务器主机地址 (默认: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=8001,
                       help='服务器端口 (默认: 8000)')
    parser.add_argument('--reload', action='store_true',
                       help='启用自动重载 (开发模式)')
    parser.add_argument('--log-level', type=str, default='info',
                       choices=['critical', 'error', 'warning', 'info', 'debug'],
                       help='日志级别 (默认: info)')

    args = parser.parse_args()

    print("="*60)
    print("消息分析API服务")
    print("="*60)
    print(f"服务地址: http://{args.host}:{args.port}")
    print(f"API文档: http://{args.host}:{args.port}/docs")
    print(f"日志级别: {args.log_level}")
    print(f"自动重载: {'启用' if args.reload else '禁用'}")
    print("="*60)

    try:
        uvicorn.run(
            "api.main:app",
            host=args.host,
            port=args.port,
            reload=args.reload,
            log_level=args.log_level
        )
    except KeyboardInterrupt:
        print("\n服务已停止")
    except Exception as e:
        print(f"启动服务时出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
