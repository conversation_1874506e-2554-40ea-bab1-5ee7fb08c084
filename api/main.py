"""
FastAPI应用主文件
提供消息分析的Web API接口
"""

import os
import json
import gzip
import tempfile
import shutil
from typing import Optional, Dict, Any
from pathlib import Path

import uvicorn
from fastapi import FastAPI, File, UploadFile, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import aiofiles

from core.message_analyzer import MessageAnalyzer


app = FastAPI(
    title="Message Analysis API",
    description="消息分析系统API，支持K-means聚类和词云分析",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局分析器实例
analyzer = MessageAnalyzer()

# 临时文件清理任务
def cleanup_temp_files(temp_dir: str):
    """清理临时文件"""
    try:
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
    except Exception as e:
        print(f"清理临时文件失败: {e}")


@app.get("/")
async def root():
    """根路径，返回API信息"""
    return {
        "message": "Message Analysis API",
        "version": "1.0.0",
        "endpoints": {
            "analyze_file": "/analyze/file",
            "analyze_json": "/analyze/json",
            "health": "/health"
        }
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "message-analysis"}


@app.post("/analyze/file")
async def analyze_file(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    config: Optional[str] = None
):
    """
    分析上传的文件
    
    支持的文件格式：
    - .json: JSON文件
    - .jsonl: JSON Lines文件  
    - .gz: gzip压缩的JSON文件
    
    Args:
        file: 上传的文件
        config: 可选的配置JSON字符串
        
    Returns:
        FileResponse: 分析结果的zip文件
    """
    if not file.filename:
        raise HTTPException(status_code=400, detail="文件名不能为空")
    
    # 检查文件类型
    allowed_extensions = {'.json', '.jsonl', '.gz'}
    file_ext = Path(file.filename).suffix.lower()
    
    if file_ext not in allowed_extensions:
        raise HTTPException(
            status_code=400, 
            detail=f"不支持的文件类型: {file_ext}。支持的类型: {', '.join(allowed_extensions)}"
        )
    
    try:
        # 解析配置
        analyzer_config = {}
        if config:
            try:
                analyzer_config = json.loads(config)
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="配置JSON格式错误")
        
        # 创建临时目录
        temp_dir = tempfile.mkdtemp(prefix="message_analysis_")
        
        # 读取文件内容
        file_content = await file.read()
        
        # 根据文件类型处理数据
        if file_ext == '.gz':
            # gzip压缩文件
            try:
                decompressed_data = gzip.decompress(file_content)
                data = json.loads(decompressed_data.decode('utf-8'))
            except (gzip.BadGzipFile, json.JSONDecodeError) as e:
                raise HTTPException(status_code=400, detail=f"gzip文件解析失败: {str(e)}")
        else:
            # JSON或JSONL文件
            try:
                content_str = file_content.decode('utf-8')
                if file_ext == '.jsonl':
                    # JSON Lines格式
                    data = []
                    for line in content_str.strip().split('\n'):
                        if line.strip():
                            data.append(json.loads(line))
                else:
                    # 标准JSON格式
                    data = json.loads(content_str)
            except (UnicodeDecodeError, json.JSONDecodeError) as e:
                raise HTTPException(status_code=400, detail=f"文件解析失败: {str(e)}")
        
        # 创建分析器实例
        if analyzer_config:
            current_analyzer = MessageAnalyzer(analyzer_config)
        else:
            current_analyzer = analyzer
        
        # 执行分析
        try:
            results = current_analyzer.analyze(data, temp_dir)
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"分析过程出错: {str(e)}")
        
        # 创建zip文件
        zip_path = current_analyzer.create_result_zip(results['output_dir'])
        
        # 添加清理任务
        background_tasks.add_task(cleanup_temp_files, temp_dir)
        background_tasks.add_task(cleanup_temp_files, os.path.dirname(zip_path))
        
        # 返回zip文件
        return FileResponse(
            path=zip_path,
            filename=f"analysis_results_{Path(file.filename).stem}.zip",
            media_type="application/zip"
        )
        
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        # 处理其他异常
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@app.post("/analyze/json")
async def analyze_json(
    background_tasks: BackgroundTasks,
    data: Dict[str, Any],
    config: Optional[Dict[str, Any]] = None
):
    """
    分析JSON数据
    
    Args:
        data: 要分析的JSON数据，格式为 {"data": [...], "filename": "optional_name"}
        config: 可选的配置字典
        
    Returns:
        FileResponse: 分析结果的zip文件
    """
    try:
        # 提取数据和文件名
        if "data" not in data:
            raise HTTPException(status_code=400, detail="请求体必须包含'data'字段")
        
        analysis_data = data["data"]
        filename = data.get("filename", "json_data")
        
        if not isinstance(analysis_data, list):
            raise HTTPException(status_code=400, detail="'data'字段必须是数组")
        
        # 创建临时目录
        temp_dir = tempfile.mkdtemp(prefix="message_analysis_")
        
        # 创建分析器实例
        if config:
            current_analyzer = MessageAnalyzer(config)
        else:
            current_analyzer = analyzer
        
        # 执行分析
        try:
            results = current_analyzer.analyze(analysis_data, temp_dir)
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"分析过程出错: {str(e)}")
        
        # 创建zip文件
        zip_path = current_analyzer.create_result_zip(results['output_dir'])
        
        # 添加清理任务
        background_tasks.add_task(cleanup_temp_files, temp_dir)
        background_tasks.add_task(cleanup_temp_files, os.path.dirname(zip_path))
        
        # 返回zip文件
        return FileResponse(
            path=zip_path,
            filename=f"analysis_results_{filename}.zip",
            media_type="application/zip"
        )
        
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        # 处理其他异常
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@app.get("/config/default")
async def get_default_config():
    """获取默认配置"""
    try:
        config = analyzer._get_default_config()
        # 转换range对象为列表，使其可以JSON序列化
        if 'k_range' in config:
            config['k_range'] = list(config['k_range'])
        return config
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}")


@app.post("/config/validate")
async def validate_config(config: Dict[str, Any]):
    """验证配置是否有效"""
    try:
        # 尝试创建分析器实例来验证配置
        test_analyzer = MessageAnalyzer(config)
        return {"valid": True, "message": "配置有效"}
    except Exception as e:
        return {"valid": False, "message": f"配置无效: {str(e)}"}


if __name__ == "__main__":
    uvicorn.run(
        "api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
