#!/usr/bin/env python3
"""
简化的命令行分析工具
保持与原有piplines/run_kmeans_analysis.py相同的使用方式
"""

import argparse
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.message_analyzer import MessageAnalyzer


def main():
    """主函数：执行完整的K-means聚类分析流程"""
    parser = argparse.ArgumentParser(description='K-means聚类分析')
    parser.add_argument('--output', '-o', type=str, default=None,
                       help='输出目录路径 (默认: analysis_results_时间戳)')
    parser.add_argument('--data-path', '-d', type=str, default=None,
                       help='log数据路径 (默认: ./data/807凌晨改写log.json)')
    parser.add_argument('--config', '-c', type=str, default=None,
                       help='配置文件路径 (JSON格式)')
    parser.add_argument('--k-range', type=str, default="10,50",
                       help='K值搜索范围，格式: start,end (默认: 10,50)')
    parser.add_argument('--max-features', type=int, default=3000,
                       help='TF-IDF最大特征数 (默认: 3000)')
    parser.add_argument('--compare-k', type=str, default="3,5,8",
                       help='对比的K值，用逗号分隔 (默认: 3,5,8)')

    args = parser.parse_args()

    # 处理交互式输入（保持原有行为）
    if args.output is None:
        print("请选择输出方式:")
        print("1. 使用默认路径 (analysis_results_时间戳)")
        print("2. 指定自定义路径")

        choice = input("请输入选择 (1/2): ").strip()

        if choice == "2":
            custom_path = input("请输入输出目录路径: ").strip()
            if custom_path:
                args.output = custom_path

    if args.data_path is None:
        print("请选择数据路径:")
        print("1. 使用默认路径 (./data/807凌晨改写log.json)")
        print("2. 指定自定义路径")

        choice = input("请输入选择 (1/2): ").strip()

        if choice == "2":
            custom_path = input("请输入数据路径: ").strip()
            if custom_path:
                args.data_path = custom_path

    # 设置默认数据路径
    if args.data_path is None:
        args.data_path = './data/807凌晨改写log.json'

    # 检查数据文件是否存在
    if not os.path.exists(args.data_path):
        print(f"错误: 数据文件不存在: {args.data_path}")
        sys.exit(1)

    # 解析配置参数
    config = {}
    
    # 解析K值范围
    try:
        k_start, k_end = map(int, args.k_range.split(','))
        config['k_range'] = range(k_start, k_end)
    except ValueError:
        print(f"错误: K值范围格式不正确: {args.k_range}")
        sys.exit(1)

    # 设置最大特征数
    config['max_features'] = args.max_features

    # 解析对比K值
    try:
        compare_k_values = [int(k.strip()) for k in args.compare_k.split(',')]
        config['compare_k_values'] = compare_k_values
    except ValueError:
        print(f"错误: 对比K值格式不正确: {args.compare_k}")
        sys.exit(1)

    # 加载额外配置文件
    if args.config:
        import json
        try:
            with open(args.config, 'r', encoding='utf-8') as f:
                file_config = json.load(f)
                config.update(file_config)
        except Exception as e:
            print(f"错误: 无法加载配置文件 {args.config}: {e}")
            sys.exit(1)

    # 创建分析器
    analyzer = MessageAnalyzer(config)

    try:
        # 执行分析
        print("开始分析...")
        results = analyzer.analyze(args.data_path, args.output)
        
        # 打印结果摘要
        print("\n" + "="*60)
        print("分析完成！")
        print("="*60)
        print(f"输出目录: {results['output_dir']}")
        print(f"最优K值: {results['optimal_k']}")
        
        if 'clustering' in results:
            clustering = results['clustering']
            print(f"轮廓系数: {clustering['silhouette_score']:.4f}")
        
        print(f"\n数据统计:")
        stats = results['data_stats']
        print(f"  原始数据: {stats['total_raw']} 条")
        print(f"  有效日志: {stats['valid_logs']} 条")
        print(f"  错误数据: {stats['error_logs']} 条")
        print(f"  聚类数据: {stats['clustering_data']} 条")
        
        print(f"\n生成的文件:")
        output_dir = results['output_dir']
        for file in os.listdir(output_dir):
            print(f"  - {file}")
        
        # 询问是否创建zip包
        create_zip = input("\n是否创建结果zip包? (y/n): ").strip().lower()
        if create_zip in ['y', 'yes']:
            zip_path = analyzer.create_result_zip(output_dir)
            print(f"zip包已创建: {zip_path}")
        
        return results['output_dir']

    except Exception as e:
        print(f"分析过程中出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
