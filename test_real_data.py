#!/usr/bin/env python3
"""
使用真实数据测试系统
"""

import json
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.message_analyzer import MessageAnalyzer


def test_real_data_analysis():
    """测试真实数据分析"""
    print("="*60)
    print("使用真实数据测试消息分析系统")
    print("="*60)
    
    # 数据文件路径
    data_file = "data/807凌晨改写log.json"
    
    if not os.path.exists(data_file):
        print(f"❌ 数据文件不存在: {data_file}")
        return False
    
    try:
        # 创建分析器，使用适合真实数据的配置
        config = {
            'k_range': range(5, 15),  # 适合真实数据的K值范围
            'max_features': 1000,     # 增加特征数以处理更复杂的文本
            'clustering_max_features': 500,
            'compare_k_values': [5, 8, 10],
            'min_text_length': 2,     # 降低最小文本长度
        }
        
        analyzer = MessageAnalyzer(config)
        
        print(f"📁 开始分析数据文件: {data_file}")
        
        # 执行分析
        results = analyzer.analyze(data_file)
        
        print(f"\n✅ 分析完成!")
        print(f"📊 分析结果:")
        print(f"  - 输出目录: {results['output_dir']}")
        print(f"  - 最优K值: {results['optimal_k']}")
        print(f"  - 数据统计:")
        for key, value in results['data_stats'].items():
            print(f"    * {key}: {value}")
        
        # 创建结果压缩包
        zip_path = analyzer.create_result_zip(results['output_dir'])
        print(f"  - 结果压缩包: {zip_path}")
        
        # 显示一些聚类结果
        if 'clustering' in results and results['clustering']:
            clustering_results = results['clustering']
            if 'results_df' in clustering_results:
                df = clustering_results['results_df']
                print(f"\n📈 聚类结果预览:")
                print(f"  - 总数据条数: {len(df)}")
                
                # 显示每个聚类的样本
                if 'cluster' in df.columns:
                    cluster_counts = df['cluster'].value_counts()
                    print(f"  - 聚类分布:")
                    for cluster_id, count in cluster_counts.items():
                        print(f"    * 聚类 {cluster_id}: {count} 条")
                        
                        # 显示该聚类的前3个样本
                        cluster_samples = df[df['cluster'] == cluster_id]['user_request'].head(3)
                        for i, sample in enumerate(cluster_samples, 1):
                            print(f"      {i}. {sample[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_api_with_real_data():
    """测试API服务处理真实数据"""
    print("\n" + "="*60)
    print("测试API服务处理真实数据")
    print("="*60)
    
    import requests
    
    # 读取真实数据的前10条作为测试
    data_file = "data/807凌晨改写log.json"
    
    if not os.path.exists(data_file):
        print(f"❌ 数据文件不存在: {data_file}")
        return False
    
    try:
        # 读取数据
        from utils import load_json
        data = load_json.load_jsonl(data_file)
        
        # 取前20条数据进行测试
        test_data = data[:20]
        print(f"📊 使用前 {len(test_data)} 条真实数据测试API")
        
        # 准备API请求
        api_url = "http://localhost:8001/analyze/json"
        request_data = {
            "data": test_data,
            "filename": "real_data_test",
            "config": {
                "k_range": [3, 8],
                "max_features": 500,
                "clustering_max_features": 200,
                "compare_k_values": [3, 5]
            }
        }
        
        print("🚀 发送API请求...")
        response = requests.post(api_url, json=request_data, timeout=120)
        
        if response.status_code == 200:
            # 保存结果
            result_file = "real_data_api_result.zip"
            with open(result_file, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ API测试成功!")
            print(f"📦 结果已保存到: {result_file}")
            return True
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务器")
        print("请确保API服务器正在运行: python start_api.py")
        return False
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🔍 开始真实数据测试...")
    
    results = []
    
    # 测试核心分析器
    print("\n1️⃣ 测试核心分析器...")
    core_result = test_real_data_analysis()
    results.append(("核心分析器", core_result))
    
    # 测试API服务
    print("\n2️⃣ 测试API服务...")
    api_result = test_api_with_real_data()
    results.append(("API服务", api_result))
    
    # 输出测试结果
    print("\n" + "="*60)
    print("🏁 真实数据测试结果汇总")
    print("="*60)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "="*60)
    if all_passed:
        print("🎉 所有真实数据测试通过!")
        print("\n💡 系统已准备好处理真实的生产数据")
    else:
        print("⚠️  部分测试失败，请检查相关组件")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
