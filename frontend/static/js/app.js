// 消息分析系统前端JavaScript

// API基础URL
const API_BASE_URL = '';

// 全局状态
let currentAnalysisId = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    loadDefaultConfig();
});

// 初始化事件监听器
function initializeEventListeners() {
    // 文件上传表单
    document.getElementById('fileUploadForm').addEventListener('submit', handleFileUpload);
    
    // JSON输入表单
    document.getElementById('jsonInputForm').addEventListener('submit', handleJsonInput);
    
    // 文件输入变化
    document.getElementById('fileInput').addEventListener('change', handleFileSelect);
    
    // 拖拽上传
    setupFileDragDrop();
}

// 加载默认配置
async function loadDefaultConfig() {
    try {
        const response = await fetch(`${API_BASE_URL}/config/default`);
        if (response.ok) {
            const config = await response.json();
            populateDefaultConfig(config);
        }
    } catch (error) {
        console.warn('无法加载默认配置:', error);
    }
}

// 填充默认配置
function populateDefaultConfig(config) {
    if (config.k_range && Array.isArray(config.k_range)) {
        const kRange = `${config.k_range[0]},${config.k_range[config.k_range.length - 1]}`;
        document.getElementById('fileKRange').value = kRange;
        document.getElementById('jsonKRange').value = kRange;
    }
    
    if (config.max_features) {
        document.getElementById('fileMaxFeatures').value = config.max_features;
        document.getElementById('jsonMaxFeatures').value = config.max_features;
    }
    
    if (config.clustering_max_features) {
        document.getElementById('fileClusteringFeatures').value = config.clustering_max_features;
        document.getElementById('jsonClusteringFeatures').value = config.clustering_max_features;
    }
    
    if (config.compare_k_values && Array.isArray(config.compare_k_values)) {
        const compareK = config.compare_k_values.join(',');
        document.getElementById('fileCompareK').value = compareK;
        document.getElementById('jsonCompareK').value = compareK;
    }
}

// 显示文件上传区域
function showFileUpload() {
    hideAllSections();
    document.getElementById('fileUploadSection').style.display = 'block';
    document.getElementById('fileUploadSection').classList.add('fade-in');
}

// 显示JSON输入区域
function showJsonInput() {
    hideAllSections();
    document.getElementById('jsonInputSection').style.display = 'block';
    document.getElementById('jsonInputSection').classList.add('fade-in');
}

// 隐藏文件上传区域
function hideFileUpload() {
    document.getElementById('fileUploadSection').style.display = 'none';
}

// 隐藏JSON输入区域
function hideJsonInput() {
    document.getElementById('jsonInputSection').style.display = 'none';
}

// 隐藏所有区域
function hideAllSections() {
    document.getElementById('fileUploadSection').style.display = 'none';
    document.getElementById('jsonInputSection').style.display = 'none';
    document.getElementById('progressSection').style.display = 'none';
    document.getElementById('resultSection').style.display = 'none';
    document.getElementById('errorSection').style.display = 'none';
}

// 处理文件选择
function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        validateFile(file);
    }
}

// 验证文件
function validateFile(file) {
    const allowedTypes = ['.json', '.jsonl', '.gz'];
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
    
    if (!allowedTypes.includes(fileExtension)) {
        showError('不支持的文件类型。请选择 .json, .jsonl 或 .gz 文件。');
        return false;
    }
    
    if (file.size > 100 * 1024 * 1024) { // 100MB限制
        showError('文件太大。请选择小于100MB的文件。');
        return false;
    }
    
    return true;
}

// 设置文件拖拽上传
function setupFileDragDrop() {
    const fileInput = document.getElementById('fileInput');
    const dropZone = fileInput.parentElement;
    
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, preventDefaults, false);
    });
    
    ['dragenter', 'dragover'].forEach(eventName => {
        dropZone.addEventListener(eventName, highlight, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, unhighlight, false);
    });
    
    dropZone.addEventListener('drop', handleDrop, false);
    
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    function highlight(e) {
        dropZone.classList.add('dragover');
    }
    
    function unhighlight(e) {
        dropZone.classList.remove('dragover');
    }
    
    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        
        if (files.length > 0) {
            fileInput.files = files;
            handleFileSelect({ target: { files: files } });
        }
    }
}

// 处理文件上传
async function handleFileUpload(event) {
    event.preventDefault();
    
    const fileInput = document.getElementById('fileInput');
    const file = fileInput.files[0];
    
    if (!file) {
        showError('请选择一个文件');
        return;
    }
    
    if (!validateFile(file)) {
        return;
    }
    
    const config = getFileUploadConfig();
    
    showProgress('准备上传文件...');
    
    try {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('config', JSON.stringify(config));
        
        updateProgress(20, '正在上传文件...');
        
        const response = await fetch(`${API_BASE_URL}/analyze/file`, {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        updateProgress(100, '分析完成！');
        
        // 处理文件下载
        const blob = await response.blob();
        const filename = getFilenameFromResponse(response) || 'analysis_result.zip';
        downloadBlob(blob, filename);
        
        showSuccess('文件分析完成！结果已自动下载。');
        
    } catch (error) {
        console.error('文件上传错误:', error);
        showError(`分析失败: ${error.message}`);
    }
}

// 处理JSON输入
async function handleJsonInput(event) {
    event.preventDefault();
    
    const jsonData = document.getElementById('jsonData').value.trim();
    const filename = document.getElementById('jsonFilename').value.trim() || 'json_analysis';
    
    if (!jsonData) {
        showError('请输入JSON数据');
        return;
    }
    
    let parsedData;
    try {
        parsedData = JSON.parse(jsonData);
    } catch (error) {
        showError('JSON格式错误: ' + error.message);
        return;
    }
    
    if (!Array.isArray(parsedData)) {
        showError('JSON数据必须是数组格式');
        return;
    }
    
    const config = getJsonInputConfig();
    
    showProgress('准备分析数据...');
    
    try {
        const requestData = {
            data: parsedData,
            filename: filename,
            config: config
        };
        
        updateProgress(20, '正在发送数据...');
        
        const response = await fetch(`${API_BASE_URL}/analyze/json`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });
        
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`HTTP ${response.status}: ${errorText}`);
        }
        
        updateProgress(100, '分析完成！');
        
        // 处理文件下载
        const blob = await response.blob();
        const resultFilename = `${filename}_result.zip`;
        downloadBlob(blob, resultFilename);
        
        showSuccess('数据分析完成！结果已自动下载。');
        
    } catch (error) {
        console.error('JSON分析错误:', error);
        showError(`分析失败: ${error.message}`);
    }
}

// 获取文件上传配置
function getFileUploadConfig() {
    const kRangeStr = document.getElementById('fileKRange').value;
    const kRangeParts = kRangeStr.split(',').map(k => parseInt(k.trim()));
    const compareKStr = document.getElementById('fileCompareK').value;
    const compareK = compareKStr.split(',').map(k => parseInt(k.trim()));

    return {
        k_range: kRangeParts, // 发送为数组，后端会处理
        max_features: parseInt(document.getElementById('fileMaxFeatures').value),
        clustering_max_features: parseInt(document.getElementById('fileClusteringFeatures').value),
        compare_k_values: compareK
    };
}

// 获取JSON输入配置
function getJsonInputConfig() {
    const kRangeStr = document.getElementById('jsonKRange').value;
    const kRangeParts = kRangeStr.split(',').map(k => parseInt(k.trim()));
    const compareKStr = document.getElementById('jsonCompareK').value;
    const compareK = compareKStr.split(',').map(k => parseInt(k.trim()));

    return {
        k_range: kRangeParts, // 发送为数组，后端会处理
        max_features: parseInt(document.getElementById('jsonMaxFeatures').value),
        clustering_max_features: parseInt(document.getElementById('jsonClusteringFeatures').value),
        compare_k_values: compareK
    };
}

// 显示进度
function showProgress(message) {
    hideAllSections();
    document.getElementById('progressSection').style.display = 'block';
    document.getElementById('progressSection').classList.add('fade-in');
    updateProgress(0, message);
}

// 更新进度
function updateProgress(percent, message) {
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    
    progressBar.style.width = `${percent}%`;
    progressBar.setAttribute('aria-valuenow', percent);
    progressText.textContent = message;
    
    if (percent === 100) {
        progressBar.classList.remove('progress-bar-animated');
        progressBar.classList.add('bg-success');
    }
}

// 显示成功消息
function showSuccess(message) {
    hideAllSections();
    document.getElementById('resultSection').style.display = 'block';
    document.getElementById('resultSection').classList.add('fade-in');
    
    document.getElementById('resultContent').innerHTML = `
        <div class="alert alert-success">
            <i class="bi bi-check-circle"></i>
            ${message}
        </div>
        <div class="text-center">
            <button class="btn btn-primary" onclick="location.reload()">
                <i class="bi bi-arrow-clockwise"></i>
                开始新的分析
            </button>
        </div>
    `;
}

// 显示错误
function showError(message) {
    hideAllSections();
    document.getElementById('errorSection').style.display = 'block';
    document.getElementById('errorSection').classList.add('fade-in');
    document.getElementById('errorContent').innerHTML = `
        <div class="error-detail">${message}</div>
        <div class="mt-3">
            <button class="btn btn-primary" onclick="hideAllSections()">
                <i class="bi bi-arrow-left"></i>
                返回
            </button>
        </div>
    `;
}

// 从响应头获取文件名
function getFilenameFromResponse(response) {
    const contentDisposition = response.headers.get('Content-Disposition');
    if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
            return filenameMatch[1];
        }
    }
    return null;
}

// 下载Blob文件
function downloadBlob(blob, filename) {
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
}

// 工具函数：格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 工具函数：格式化时间
function formatTime(seconds) {
    if (seconds < 60) {
        return `${seconds.toFixed(1)}秒`;
    } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}分${remainingSeconds.toFixed(0)}秒`;
    } else {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        return `${hours}小时${minutes}分`;
    }
}
