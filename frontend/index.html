<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息分析系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/ui/static/css/style.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-graph-up"></i>
                消息分析系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/docs" target="_blank">
                    <i class="bi bi-book"></i>
                    API文档
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="text-center">
                    <i class="bi bi-chat-dots text-primary"></i>
                    消息聚类分析平台
                </h1>
                <p class="text-center text-muted">
                    基于K-means算法的智能消息分类与分析系统
                </p>
            </div>
        </div>

        <!-- 功能选择卡片 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card h-100 border-primary">
                    <div class="card-body text-center">
                        <i class="bi bi-file-earmark-text display-4 text-primary mb-3"></i>
                        <h5 class="card-title">文件上传分析</h5>
                        <p class="card-text">上传JSON或JSONL格式的消息数据文件进行分析</p>
                        <button class="btn btn-primary" onclick="showFileUpload()">
                            <i class="bi bi-upload"></i>
                            选择文件
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100 border-success">
                    <div class="card-body text-center">
                        <i class="bi bi-code-square display-4 text-success mb-3"></i>
                        <h5 class="card-title">JSON数据分析</h5>
                        <p class="card-text">直接输入JSON格式的消息数据进行实时分析</p>
                        <button class="btn btn-success" onclick="showJsonInput()">
                            <i class="bi bi-pencil-square"></i>
                            输入数据
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 文件上传区域 -->
        <div id="fileUploadSection" class="card mb-4" style="display: none;">
            <div class="card-header">
                <h5><i class="bi bi-cloud-upload"></i> 文件上传分析</h5>
            </div>
            <div class="card-body">
                <form id="fileUploadForm">
                    <div class="mb-3">
                        <label for="fileInput" class="form-label">选择数据文件</label>
                        <input type="file" class="form-control" id="fileInput" accept=".json,.jsonl,.gz">
                        <div class="form-text">支持 .json, .jsonl, .gz 格式文件</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">分析配置</label>
                        <div class="row">
                            <div class="col-md-3">
                                <label for="fileKRange" class="form-label">K值范围</label>
                                <input type="text" class="form-control" id="fileKRange" value="5,15" placeholder="5,15">
                            </div>
                            <div class="col-md-3">
                                <label for="fileMaxFeatures" class="form-label">最大特征数</label>
                                <input type="number" class="form-control" id="fileMaxFeatures" value="1000">
                            </div>
                            <div class="col-md-3">
                                <label for="fileClusteringFeatures" class="form-label">聚类特征数</label>
                                <input type="number" class="form-control" id="fileClusteringFeatures" value="500">
                            </div>
                            <div class="col-md-3">
                                <label for="fileCompareK" class="form-label">对比K值</label>
                                <input type="text" class="form-control" id="fileCompareK" value="5,8,10" placeholder="5,8,10">
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-play-circle"></i>
                        开始分析
                    </button>
                    <button type="button" class="btn btn-secondary ms-2" onclick="hideFileUpload()">
                        <i class="bi bi-x-circle"></i>
                        取消
                    </button>
                </form>
            </div>
        </div>

        <!-- JSON输入区域 -->
        <div id="jsonInputSection" class="card mb-4" style="display: none;">
            <div class="card-header">
                <h5><i class="bi bi-braces"></i> JSON数据分析</h5>
            </div>
            <div class="card-body">
                <form id="jsonInputForm">
                    <div class="mb-3">
                        <label for="jsonData" class="form-label">JSON数据</label>
                        <textarea class="form-control" id="jsonData" rows="10" placeholder='[
  {
    "msgId": "msg_001",
    "message": {
      "user_request": "我想查询订单状态",
      "response": {"解释": "正在为您查询订单信息"}
    },
    "context": "{\"user_id\": \"123\"}"
  }
]'></textarea>
                        <div class="form-text">请输入JSON格式的消息数据数组</div>
                    </div>
                    <div class="mb-3">
                        <label for="jsonFilename" class="form-label">结果文件名</label>
                        <input type="text" class="form-control" id="jsonFilename" value="json_analysis" placeholder="json_analysis">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">分析配置</label>
                        <div class="row">
                            <div class="col-md-3">
                                <label for="jsonKRange" class="form-label">K值范围</label>
                                <input type="text" class="form-control" id="jsonKRange" value="3,8" placeholder="3,8">
                            </div>
                            <div class="col-md-3">
                                <label for="jsonMaxFeatures" class="form-label">最大特征数</label>
                                <input type="number" class="form-control" id="jsonMaxFeatures" value="500">
                            </div>
                            <div class="col-md-3">
                                <label for="jsonClusteringFeatures" class="form-label">聚类特征数</label>
                                <input type="number" class="form-control" id="jsonClusteringFeatures" value="200">
                            </div>
                            <div class="col-md-3">
                                <label for="jsonCompareK" class="form-label">对比K值</label>
                                <input type="text" class="form-control" id="jsonCompareK" value="3,5" placeholder="3,5">
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-play-circle"></i>
                        开始分析
                    </button>
                    <button type="button" class="btn btn-secondary ms-2" onclick="hideJsonInput()">
                        <i class="bi bi-x-circle"></i>
                        取消
                    </button>
                </form>
            </div>
        </div>

        <!-- 进度显示区域 -->
        <div id="progressSection" class="card mb-4" style="display: none;">
            <div class="card-header">
                <h5><i class="bi bi-hourglass-split"></i> 分析进度</h5>
            </div>
            <div class="card-body">
                <div class="progress mb-3">
                    <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                         role="progressbar" style="width: 0%"></div>
                </div>
                <div id="progressText" class="text-center">准备开始分析...</div>
            </div>
        </div>

        <!-- 结果显示区域 -->
        <div id="resultSection" class="card mb-4" style="display: none;">
            <div class="card-header">
                <h5><i class="bi bi-check-circle text-success"></i> 分析结果</h5>
            </div>
            <div class="card-body">
                <div id="resultContent"></div>
            </div>
        </div>

        <!-- 错误显示区域 -->
        <div id="errorSection" class="alert alert-danger" style="display: none;">
            <h5><i class="bi bi-exclamation-triangle"></i> 错误信息</h5>
            <div id="errorContent"></div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-light mt-5 py-4">
        <div class="container text-center">
            <p class="text-muted mb-0">
                <i class="bi bi-gear"></i>
                消息分析系统 - 基于K-means聚类算法的智能分析平台
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/ui/static/js/app.js"></script>
</body>
</html>
