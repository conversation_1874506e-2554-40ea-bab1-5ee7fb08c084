"""
运行K-means聚类分析的主程序
"""

import sys
import os
from datetime import datetime
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils import (
    load_json,
    spilit_log_message,
    clean_log,
    jieba_optimized_common_substring,
    create_wordcloud
)
from utils.kmeans_clustering import (
    perform_kmeans_clustering,
    visualize_clusters,
    find_optimal_k
)

def create_output_directory(output_path=None):
    """
    创建输出目录

    Args:
        output_path: 指定的输出路径，如果为None则使用默认路径

    Returns:
        str: 创建的输出目录路径
    """
    if output_path is None:
        # 使用默认路径：当前时间戳
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = f"kmeans_clustering_results_{timestamp}"

    # 确保输出目录存在
    if not os.path.exists(output_path):
        os.makedirs(output_path)
        print(f"创建输出目录: {output_path}")
    else:
        print(f"使用输出目录: {output_path}")

    return output_path

def main(output_path=None,data_path=None):
    """
    主函数：执行完整的K-means聚类分析流程

    Args:
        output_path: 输出路径，如果为None则使用默认路径
        data_path: 数据文件路径
    """
    print("开始加载数据...")

    # 创建输出目录
    output_dir = create_output_directory(output_path)

    # 加载数据
    data = load_json.load_jsonl(data_path or '/Users/<USER>/Desktop/message-prossor/data/807凌晨改写log.json') 
    log_data, error_data = spilit_log_message.split_log_pre_message(data)
    clean_data = clean_log.clean_log(log_data)
    log_data = clean_data
    print(f"成功加载数据: {len(data)} 条原始数据")
    print(f"有效日志数据: {len(log_data)} 条")
    print(f"错误数据: {len(error_data)} 条")
    
    if len(log_data) == 0:
        print("没有有效的日志数据，程序退出")
        return
    
    # 准备带有msgId的数据
    prepared_data = prepare_data_with_msgid(log_data)
    print(f"准备用于聚类的数据: {len(prepared_data)} 条")
    
    if len(prepared_data) == 0:
        print("没有有效的用户请求数据，程序退出")
        return
    
    # 1. 寻找最优聚类数量
    print("\n" + "="*60)
    print("第一步：寻找最优聚类数量")
    print("="*60)
    
    try:
        evaluation_results, best_k = find_optimal_k(
            prepared_data, 
            k_range=range(10, 50), 
            max_features=3000
        )
    except Exception as e:
        print(f"寻找最优k值时出错: {e}")
        best_k = 5  # 使用默认值
        print(f"使用默认聚类数量: {best_k}")
    
    # 2. 使用最优k值进行详细聚类分析
    print("\n" + "="*60)
    print(f"第二步：使用最优k值({best_k})进行详细聚类分析")
    print("="*60)
    
    clustering_results = perform_kmeans_clustering(
        prepared_data, 
        n_clusters=best_k, 
        max_features=1000,
        random_state=42
    )
    results_df = clustering_results['results_df']
    clustering_results['results_df'] = add_msgid_mapping(results_df, log_data)
    # 3. 打印分析结果
    print_cluster_summary_with_msgid(clustering_results)
    
    # 4. 可视化结果
    print("\n正在生成可视化图表...")
    try:
        visualization_path = os.path.join(output_dir, f'user_request_clusters_k{best_k}.png')
        visualize_clusters(
            clustering_results,
            save_path=visualization_path
        )
    except Exception as e:
        print(f"可视化时出错: {e}")

    # 5. 保存详细结果（已包含msgId）
    print("\n正在保存结果...")
    try:
        results_df = clustering_results['results_df']
        results_df = add_msgid_mapping(results_df, log_data)
        csv_path = os.path.join(output_dir, f'user_request_clustering_results_k{best_k}.csv')
        results_df.to_csv(
            csv_path,
            index=False,
            encoding='utf-8-sig'
        )
        print(f"详细聚类结果已保存到: {csv_path}")

        # 保存聚类摘要
        summary_path = os.path.join(output_dir, f'cluster_summary_k{best_k}.txt')
        save_cluster_summary(clustering_results, summary_path)

    except Exception as e:
        print(f"保存结果时出错: {e}")
    
    # 6. 额外分析：尝试其他k值进行对比
    print("\n" + "="*60)
    print("第三步：对比分析不同k值的聚类效果")
    print("="*60)
    
    compare_k_values = [3, 5, 8]
    if best_k not in compare_k_values:
        compare_k_values.append(best_k)
    
    for k in sorted(set(compare_k_values)):
        if k == best_k:
            continue  # 已经分析过了
            
        print(f"\n分析 k={k} 的聚类效果...")
        try:
            results = perform_kmeans_clustering(
                prepared_data, 
                n_clusters=k, 
                max_features=1000,
                random_state=42
            )
            
            print(f"k={k} 聚类结果摘要:")
            print(f"  轮廓系数: {results['silhouette_score']:.4f}")
            print(f"  数据量: {len(results['results_df'])}")
            
            # 保存结果（已包含msgId）
            csv_path = os.path.join(output_dir, f'user_request_clustering_results_k{k}.csv')
            results['results_df'].to_csv(
                csv_path,
                index=False,
                encoding='utf-8-sig'
            )
            
        except Exception as e:
            print(f"分析 k={k} 时出错: {e}")
    # 7.关键词分析+词云
    enhanced_human_data, word_frequency = jieba_optimized_common_substring.extract_keywords_from_pre_user_data(log_data)
    message = create_wordcloud.create_chinese_wordcloud_excel_quality(
        word_frequency, 
        filename=os.path.join(output_dir, 'chinese_wordcloud_quality.xlsx'), 
    )
    print("\n" + "="*60)
    print("K-means聚类分析完成！")
    print("="*60)
    print(f"所有结果已保存到目录: {output_dir}")
    print("生成的文件:")
    print(f"- user_request_clusters_k{best_k}.png (可视化图表)")
    print(f"- user_request_clustering_results_k{best_k}.csv (详细结果，包含msgId)")
    print(f"- cluster_summary_k{best_k}.txt (聚类摘要)")
    for k in compare_k_values:
        if k != best_k:
            print(f"- user_request_clustering_results_k{k}.csv (k={k}的结果，包含msgId)")
    print(message)
    return output_dir

def save_cluster_summary(clustering_results, filename):
    """
    保存聚类摘要到文件
    
    Args:
        clustering_results: 聚类结果字典
        filename: 保存文件名
    """
    cluster_info = clustering_results['cluster_info']
    silhouette_score_val = clustering_results['silhouette_score']
    n_clusters = clustering_results['n_clusters']
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write("K-means聚类分析摘要报告\n")
        f.write("="*50 + "\n\n")
        f.write(f"聚类数量: {n_clusters}\n")
        f.write(f"轮廓系数: {silhouette_score_val:.4f}\n")
        f.write(f"总数据量: {len(clustering_results['results_df'])}\n\n")
        
        for cluster_id in range(n_clusters):
            info = cluster_info[cluster_id]
            f.write(f"【聚类 {cluster_id}】\n")
            f.write(f"数据量: {info['size']} ({info['percentage']:.1f}%)\n")
            
            f.write("主要特征词:\n")
            for feature, score in info['top_features'][:10]:
                f.write(f"  - {feature}: {score:.4f}\n")
            
            f.write("样本文本 (含msgId):\n")
            # 获取该聚类的样本数据，包含msgId
            cluster_samples = clustering_results['results_df'][
                clustering_results['results_df']['cluster'] == cluster_id
            ].head(5)
            
            for i, (_, row) in enumerate(cluster_samples.iterrows(), 1):
                msgid = row.get('msgId', 'unknown')
                text = row['user_request']
                f.write(f"  {i}. [msgId: {msgid}] {text}\n")
            f.write("\n" + "-"*40 + "\n\n")
    
    print(f"聚类摘要已保存到: {filename}")

def analyze_specific_cluster(clustering_results, cluster_id):
    """
    分析特定聚类的详细信息
    
    Args:
        clustering_results: 聚类结果字典
        cluster_id: 聚类ID
    """
    if cluster_id not in clustering_results['cluster_info']:
        print(f"聚类 {cluster_id} 不存在")
        return
    
    info = clustering_results['cluster_info'][cluster_id]
    results_df = clustering_results['results_df']
    cluster_data = results_df[results_df['cluster'] == cluster_id]
    
    print(f"\n聚类 {cluster_id} 详细分析:")
    print(f"数据量: {info['size']} ({info['percentage']:.1f}%)")
    
    print("\n主要特征词:")
    for feature, score in info['top_features']:
        print(f"  - {feature}: {score:.4f}")
    
    print(f"\n所有文本 (含msgId) ({len(cluster_data)} 条):")
    for i, (_, row) in enumerate(cluster_data.iterrows(), 1):
        msgid = row.get('msgId', 'unknown')
        text = row['user_request']
        print(f"  {i}. [msgId: {msgid}] {text}")

def print_cluster_summary_with_msgid(clustering_results):
    """
    打印包含msgId的聚类摘要
    """
    cluster_info = clustering_results['cluster_info']
    n_clusters = clustering_results['n_clusters']
    silhouette_score_val = clustering_results['silhouette_score']
    
    print("\n" + "="*80)
    print(f"K-means聚类分析结果 (k={n_clusters})")
    print("="*80)
    print(f"轮廓系数: {silhouette_score_val:.4f}")
    print(f"总数据量: {len(clustering_results['results_df'])}")
    
    for cluster_id in range(n_clusters):
        info = cluster_info[cluster_id]
        print(f"\n【聚类 {cluster_id}】")
        print(f"数据量: {info['size']} ({info['percentage']:.1f}%)")
        
        print("主要特征词:")
        for feature, score in info['top_features'][:5]:
            print(f"  - {feature}: {score:.4f}")
        
        print("样本文本 (含msgId):")
        # 获取该聚类的样本数据
        cluster_samples = clustering_results['results_df'][
            clustering_results['results_df']['cluster'] == cluster_id
        ].head(3)
        
        for i, (_, row) in enumerate(cluster_samples.iterrows(), 1):
            msgid = row.get('msgId', 'unknown')
            text = row['user_request']
            print(f"  {i}. [msgId: {msgid}] {text[:100]}{'...' if len(text) > 100 else ''}")
        print()

def add_msgid_mapping(results_df, log_data):
    """
    为聚类结果添加msgId映射
    
    Args:
        results_df: 聚类结果DataFrame
        log_data: 原始日志数据
    
    Returns:
        DataFrame: 包含msgId的聚类结果
    """
    # 提取msgId映射
    msgid_mapping = {}
    for i, item in enumerate(log_data):
        try:
            # 尝试从context中提取msgId
            if 'context' in item:
                import json
                context = json.loads(item['context']) if isinstance(item['context'], str) else item['context']
                msgid = context.get('msgId', f'unknown_{i}')
            else:
                msgid = f'unknown_{i}'
            msgid_mapping[i] = msgid
        except (KeyError, TypeError, json.JSONDecodeError):
            msgid_mapping[i] = f'unknown_{i}'
    
    # 为results_df添加msgId列
    results_df_copy = results_df.copy()
    results_df_copy['msgId'] = results_df_copy['original_index'].map(msgid_mapping)
    
    # 重新排列列的顺序，将msgId放在前面
    columns = ['msgId', 'original_index', 'user_request', 'processed_text', 'cluster']
    results_df_copy = results_df_copy[columns]
    
    return results_df_copy

def prepare_data_with_msgid(log_data):
    """
    准备带有msgId的数据用于聚类分析
    
    Args:
        log_data: 原始日志数据
    
    Returns:
        list: 包含msgId和文本的数据列表
    """
    prepared_data = []
    
    for i, item in enumerate(log_data):
        try:
            # 提取用户请求文本
            user_request = item['message']['user_request']
            if not user_request or not isinstance(user_request, str) or len(user_request.strip()) == 0:
                continue
            
            # 提取msgId
            msgid = f'unknown_{i}'  # 默认值
            if 'context' in item:
                import json
                context = json.loads(item['context']) if isinstance(item['context'], str) else item['context']
                msgid = context.get('msgId', f'unknown_{i}')
            prepared_data.append({
                'original_index': i,
                'message':{
                    'user_request': user_request.strip()
                },
                'user_request': user_request.strip()
            })
            
        except (KeyError, TypeError, json.JSONDecodeError) as e:
            print(f"处理索引 {i} 时出错: {e}")
            continue
    
    return prepared_data

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='K-means聚类分析')
    parser.add_argument('--output', '-o', type=str, default=None,
                       help='输出目录路径 (默认: kmeans_clustering_results_时间戳)')
    parser.add_argument('--data-path', '-d', type=str, default=None,
                       help='log数据路径 (默认: ./data/807凌晨改写log.json)')

    args = parser.parse_args()

    # 如果没有指定输出路径，询问用户
    if args.output is None:
        print("请选择输出方式:")
        print("1. 使用默认路径 (kmeans_clustering_results_时间戳)")
        print("2. 指定自定义路径")

        choice = input("请输入选择 (1/2): ").strip()

        if choice == "2":
            custom_path = input("请输入输出目录路径: ").strip()
            if custom_path:
                args.output = custom_path
    if args.data_path is None:
        print("请选择数据路径:")
        print("1. 使用默认路径 (./data/807凌晨改写log.json)")
        print("2. 指定自定义路径")

        choice = input("请输入选择 (1/2): ").strip()

        if choice == "2":
            custom_path = input("请输入数据路径: ").strip()
            if custom_path:
                args.data_path = custom_path

    main(args.output,args.data_path)
