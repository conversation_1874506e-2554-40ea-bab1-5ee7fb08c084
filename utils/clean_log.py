CLEAN_LIST = [
    'https://mobile.yangkeduo.com/goods.html?goods_id=','[大爱]','[订单消息]','订单编号:'
]
LIMIT_LEN = 3

def clean_log(log_data):
    new_log_data = []
    for item in log_data:
        e = 1
        if len(item['message']['user_request']) < LIMIT_LEN:
            e = 0
            continue
        for j in CLEAN_LIST:
            if j in item['message']['user_request']:
                e = 0 
                continue
        
        if e == 1:
            new_log_data.append(item)
            continue
    return new_log_data