"""
K-means聚类分析模块
用于对用户请求进行聚类分析
"""

import numpy as np
import pandas as pd
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import KMeans
from sklearn.metrics import silhouette_score
from sklearn.decomposition import PCA
import matplotlib.pyplot as plt
import seaborn as sns
import jieba
import re
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def preprocess_text(text):
    """
    文本预处理函数
    
    Args:
        text: 输入文本
    
    Returns:
        str: 预处理后的文本
    """
    if not isinstance(text, str):
        return ""
    
    # 去除特殊字符，保留中文、英文、数字
    text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', ' ', text)
    
    # 去除多余空格
    text = re.sub(r'\s+', ' ', text).strip()
    
    # 使用jieba分词
    words = jieba.cut(text)
    
    # 过滤停用词和短词
    stop_words = {
        '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', 
        '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', 
        '没有', '看', '好', '自己', '这', '那', '个', '为', '能', '可以',
        '什么', '怎么', '如何', '吗', '呢', '吧', '啊', '呀'
    }
    filtered_words = [word.strip() for word in words 
                     if len(word.strip()) > 1 and word.strip() not in stop_words]
    
    return ' '.join(filtered_words)

def extract_user_requests(log_data):
    """
    从log_data中提取用户请求文本
    
    Args:
        log_data: 日志数据列表
    
    Returns:
        tuple: (user_requests, valid_indices)
    """
    user_requests = []
    valid_indices = []
    
    print("正在提取用户请求文本...")
    for i, item in enumerate(log_data):
        try:
            user_request = item['message']['user_request']
            if user_request and isinstance(user_request, str) and len(user_request.strip()) > 0:
                user_requests.append(user_request.strip())
                valid_indices.append(i)
        except (KeyError, TypeError) as e:
            print(f"索引 {i} 处理失败: {e}")
            continue
    
    print(f"成功提取 {len(user_requests)} 条有效用户请求")
    return user_requests, valid_indices

def perform_kmeans_clustering(log_data, n_clusters=5, max_features=1000, random_state=42):
    """
    对用户请求进行K-means聚类
    
    Args:
        log_data: 日志数据列表
        n_clusters: 聚类数量
        max_features: TF-IDF最大特征数
        random_state: 随机种子
    
    Returns:
        dict: 包含聚类结果的字典
    """
    
    # 提取用户请求文本
    user_requests, valid_indices = extract_user_requests(log_data)
    
    if len(user_requests) < n_clusters:
        print(f"警告: 有效数据量({len(user_requests)})少于聚类数量({n_clusters})")
        n_clusters = max(2, len(user_requests) // 2)
        print(f"调整聚类数量为: {n_clusters}")
    
    # 文本预处理
    print("正在进行文本预处理...")
    processed_texts = [preprocess_text(text) for text in user_requests]
    
    # 过滤空文本
    non_empty_texts = []
    non_empty_indices = []
    for i, text in enumerate(processed_texts):
        if text.strip():
            non_empty_texts.append(text)
            non_empty_indices.append(valid_indices[i])
    
    print(f"预处理后有效文本数量: {len(non_empty_texts)}")
    
    if len(non_empty_texts) < n_clusters:
        print(f"警告: 预处理后有效数据量({len(non_empty_texts)})少于聚类数量({n_clusters})")
        n_clusters = max(2, len(non_empty_texts) // 2)
        print(f"再次调整聚类数量为: {n_clusters}")
    
    # TF-IDF向量化
    print("正在进行TF-IDF向量化...")
    vectorizer = TfidfVectorizer(
        max_features=max_features,
        ngram_range=(1, 2),  # 使用1-gram和2-gram
        min_df=2,  # 至少出现2次
        max_df=0.8  # 最多出现在80%的文档中
    )
    
    tfidf_matrix = vectorizer.fit_transform(non_empty_texts)
    feature_names = vectorizer.get_feature_names_out()
    
    print(f"TF-IDF矩阵形状: {tfidf_matrix.shape}")
    
    # K-means聚类
    print(f"正在进行K-means聚类 (k={n_clusters})...")
    kmeans = KMeans(n_clusters=n_clusters, random_state=random_state, n_init=10)
    cluster_labels = kmeans.fit_predict(tfidf_matrix)
    
    # 计算轮廓系数
    if len(set(cluster_labels)) > 1:
        silhouette_avg = silhouette_score(tfidf_matrix, cluster_labels)
        print(f"轮廓系数: {silhouette_avg:.4f}")
    else:
        silhouette_avg = 0
        print("所有数据被分到同一类，无法计算轮廓系数")
    
    # 分析每个聚类的特征词
    print("正在分析聚类特征...")
    cluster_info = analyze_clusters(
        n_clusters, cluster_labels, non_empty_texts, 
        user_requests, non_empty_indices, valid_indices, 
        kmeans, feature_names
    )
    
    # 创建结果DataFrame
    results_df = pd.DataFrame({
        'original_index': non_empty_indices,
        'user_request': [user_requests[valid_indices.index(idx)] for idx in non_empty_indices],
        'processed_text': non_empty_texts,
        'cluster': cluster_labels
    })
    
    return {
        'results_df': results_df,
        'cluster_info': cluster_info,
        'silhouette_score': silhouette_avg,
        'vectorizer': vectorizer,
        'kmeans_model': kmeans,
        'tfidf_matrix': tfidf_matrix,
        'n_clusters': n_clusters
    }

def analyze_clusters(n_clusters, cluster_labels, non_empty_texts, 
                    user_requests, non_empty_indices, valid_indices, 
                    kmeans, feature_names):
    """
    分析聚类特征
    """
    cluster_info = {}
    
    for cluster_id in range(n_clusters):
        cluster_indices = np.where(cluster_labels == cluster_id)[0]
        cluster_texts = [non_empty_texts[i] for i in cluster_indices]
        cluster_original_texts = [
            user_requests[valid_indices.index(non_empty_indices[i])] 
            for i in cluster_indices
        ]
        
        # 计算该聚类的中心向量
        cluster_center = kmeans.cluster_centers_[cluster_id]
        
        # 找出最重要的特征词
        top_features_idx = cluster_center.argsort()[-10:][::-1]  # 前10个重要特征
        top_features = [feature_names[i] for i in top_features_idx]
        top_scores = [cluster_center[i] for i in top_features_idx]
        
        cluster_info[cluster_id] = {
            'size': len(cluster_indices),
            'percentage': len(cluster_indices) / len(non_empty_texts) * 100,
            'top_features': list(zip(top_features, top_scores)),
            'sample_texts': cluster_original_texts[:5],  # 前5个样本
            'all_texts': cluster_original_texts
        }
    
    return cluster_info

def visualize_clusters(clustering_results, save_path=None):
    """
    可视化聚类结果
    
    Args:
        clustering_results: 聚类结果字典
        save_path: 保存路径
    
    Returns:
        numpy.ndarray: PCA降维后的结果
    """
    tfidf_matrix = clustering_results['tfidf_matrix']
    cluster_labels = clustering_results['results_df']['cluster'].values
    n_clusters = clustering_results['n_clusters']
    
    # 使用PCA降维到2D
    print("正在进行PCA降维...")
    pca = PCA(n_components=2, random_state=42)
    pca_result = pca.fit_transform(tfidf_matrix.toarray())
    
    # 创建可视化
    plt.figure(figsize=(12, 8))
    
    # 绘制散点图
    colors = plt.cm.Set3(np.linspace(0, 1, n_clusters))
    for i in range(n_clusters):
        cluster_points = pca_result[cluster_labels == i]
        plt.scatter(cluster_points[:, 0], cluster_points[:, 1], 
                   c=[colors[i]], label=f'聚类 {i}', alpha=0.7, s=50)
    
    plt.title('用户请求K-means聚类结果 (PCA降维)', fontsize=16)
    plt.xlabel(f'第一主成分 (解释方差: {pca.explained_variance_ratio_[0]:.2%})', fontsize=12)
    plt.ylabel(f'第二主成分 (解释方差: {pca.explained_variance_ratio_[1]:.2%})', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"聚类可视化图已保存到: {save_path}")

    # 不自动显示图表，避免在服务器环境中出错
    # try:
    #     # plt.show()
    # except Exception as e:
    #     print(f"显示图表时出错: {e}")
    #     print("图表已保存到文件，可以手动查看")
    
    return pca_result

def print_cluster_analysis(clustering_results):
    """
    打印聚类分析结果
    
    Args:
        clustering_results: 聚类结果字典
    """
    cluster_info = clustering_results['cluster_info']
    silhouette_score_val = clustering_results['silhouette_score']
    n_clusters = clustering_results['n_clusters']
    
    print("\n" + "="*80)
    print(f"K-means聚类分析结果 (k={n_clusters})")
    print("="*80)
    print(f"轮廓系数: {silhouette_score_val:.4f}")
    print(f"总数据量: {len(clustering_results['results_df'])}")
    
    for cluster_id in range(n_clusters):
        info = cluster_info[cluster_id]
        print(f"\n【聚类 {cluster_id}】")
        print(f"数据量: {info['size']} ({info['percentage']:.1f}%)")
        
        print("主要特征词:")
        for feature, score in info['top_features'][:5]:
            print(f"  - {feature}: {score:.4f}")
        
        print("样本文本:")
        for i, text in enumerate(info['sample_texts'][:3], 1):
            print(f"  {i}. {text[:100]}{'...' if len(text) > 100 else ''}")
        print()

def find_optimal_k(log_data, k_range=range(2, 11), max_features=1000):
    """
    寻找最优的聚类数量
    
    Args:
        log_data: 日志数据
        k_range: 聚类数量范围
        max_features: TF-IDF最大特征数
    
    Returns:
        dict: 包含不同k值的评估结果
    """
    user_requests, valid_indices = extract_user_requests(log_data)
    processed_texts = [preprocess_text(text) for text in user_requests]
    non_empty_texts = [text for text in processed_texts if text.strip()]
    
    if len(non_empty_texts) < max(k_range):
        print(f"数据量不足，调整k值范围")
        k_range = range(2, min(len(non_empty_texts), max(k_range)))
    
    # TF-IDF向量化
    vectorizer = TfidfVectorizer(
        max_features=max_features,
        ngram_range=(1, 2),
        min_df=2,
        max_df=0.8
    )
    tfidf_matrix = vectorizer.fit_transform(non_empty_texts)
    
    results = {}
    silhouette_scores = []
    inertias = []
    
    for k in k_range:
        print(f"评估 k={k}...")
        kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
        cluster_labels = kmeans.fit_predict(tfidf_matrix)
        
        # 计算轮廓系数
        silhouette_avg = silhouette_score(tfidf_matrix, cluster_labels)
        silhouette_scores.append(silhouette_avg)
        inertias.append(kmeans.inertia_)
        
        results[k] = {
            'silhouette_score': silhouette_avg,
            'inertia': kmeans.inertia_
        }
        
        print(f"k={k}, 轮廓系数: {silhouette_avg:.4f}, 惯性: {kmeans.inertia_:.2f}")
    
    # 可视化评估结果
    plt.figure(figsize=(15, 5))
    
    plt.subplot(1, 2, 1)
    plt.plot(k_range, silhouette_scores, 'bo-')
    plt.title('轮廓系数 vs 聚类数量')
    plt.xlabel('聚类数量 (k)')
    plt.ylabel('轮廓系数')
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    plt.plot(k_range, inertias, 'ro-')
    plt.title('惯性 vs 聚类数量 (肘部法则)')
    plt.xlabel('聚类数量 (k)')
    plt.ylabel('惯性')
    plt.grid(True)
    
    plt.tight_layout()

    # 保存图表
    try:
        plt.savefig('optimal_k_analysis.png', dpi=300, bbox_inches='tight')
        print("最优k值分析图已保存到: optimal_k_analysis.png")
    except Exception as e:
        print(f"保存图表时出错: {e}")

    # 不自动显示图表，避免在服务器环境中出错
    # try:
    #     plt.show()
    # except Exception as e:
    #     print(f"显示图表时出错: {e}")
    #     print("图表已保存到文件，可以手动查看")
    
    # 推荐最优k值
    best_k = k_range[np.argmax(silhouette_scores)]
    print(f"\n推荐的最优聚类数量: k={best_k} (轮廓系数: {max(silhouette_scores):.4f})")
    
    return results, best_k
