import ijson
import json
from tqdm import tqdm


def _load_json(file_path):
    with open(file_path, "r", encoding="utf-8") as file:
        data = []
        for item in ijson.items(file, "item"):
            data.append(item)
            if len(data) >= 10000:
                yield data
                data.clear()
        if data:
            yield data


def _load_jsonl(file_path):
    with open(file_path, "r", encoding="utf-8") as file:
        data = []
        for line in file:
            item = json.loads(line)
            data.append(item)
            if len(data) >= 10000:
                yield data
                data.clear()
        if data:
            yield data


def load_json(file_path):
    data = []
    for batch in tqdm(_load_json(file_path), desc=f"Loading JSON data...{file_path}"):
        data.extend(batch)
    return data


def load_jsonl(file_path):
    data = []
    for batch in tqdm(
        _load_jsonl(file_path), desc=f"Loading JSON Lines data...{file_path}"
    ):
        data.extend(batch)
    return data
