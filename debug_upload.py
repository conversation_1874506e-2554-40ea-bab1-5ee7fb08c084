#!/usr/bin/env python3
"""
调试文件上传问题
"""

import requests
import json
import tempfile
import os


def test_health():
    """测试健康检查"""
    try:
        response = requests.get("http://localhost:8001/health")
        print(f"健康检查: {response.status_code} - {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"健康检查失败: {e}")
        return False


def test_config():
    """测试配置获取"""
    try:
        response = requests.get("http://localhost:8001/config/default")
        print(f"配置获取: {response.status_code}")
        if response.status_code == 200:
            config = response.json()
            print(f"默认配置: {config}")
        return response.status_code == 200
    except Exception as e:
        print(f"配置获取失败: {e}")
        return False


def test_simple_json():
    """测试简单JSON分析"""
    # 创建更多测试数据
    test_data = []
    for i in range(20):
        test_data.append({
            "msgId": f"test_{i:03d}",
            "message": {
                "user_request": f"这是测试消息{i}，内容包括查询订单状态和退款问题",
                "response": {"解释": f"这是回复{i}"}
            },
            "context": f"{{\"user_id\": \"{i}\"}}"
        })
    
    request_data = {
        "data": test_data,
        "filename": "debug_test",
        "config": {
            "k_range": [2, 5],
            "max_features": 50,
            "clustering_max_features": 30,
            "compare_k_values": [2, 3]
        }
    }
    
    try:
        print("发送JSON分析请求...")
        response = requests.post(
            "http://localhost:8001/analyze/json",
            json=request_data,
            timeout=120
        )
        
        print(f"JSON分析响应: {response.status_code}")
        if response.status_code != 200:
            print(f"错误详情: {response.text}")
            return False
        
        # 保存结果
        with open("debug_json_result.zip", "wb") as f:
            f.write(response.content)
        print("JSON分析成功，结果已保存")
        return True
        
    except Exception as e:
        print(f"JSON分析失败: {e}")
        return False


def test_file_upload():
    """测试文件上传"""
    # 创建测试文件
    test_data = []
    for i in range(20):
        test_data.append({
            "msgId": f"file_test_{i:03d}",
            "message": {
                "user_request": f"文件测试消息{i}，包含各种不同的内容和关键词",
                "response": {"解释": f"文件测试回复{i}"}
            },
            "context": f"{{\"user_id\": \"file_{i}\"}}"
        })
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
        temp_file = f.name
    
    try:
        # 测试文件上传
        with open(temp_file, 'rb') as f:
            files = {'file': ('test_data.json', f, 'application/json')}
            data = {
                'config': json.dumps({
                    "k_range": [2, 5],
                    "max_features": 50,
                    "clustering_max_features": 30,
                    "compare_k_values": [2, 3]
                })
            }
            
            print("发送文件上传请求...")
            response = requests.post(
                "http://localhost:8001/analyze/file",
                files=files,
                data=data,
                timeout=120
            )
        
        print(f"文件上传响应: {response.status_code}")
        if response.status_code != 200:
            print(f"错误详情: {response.text}")
            return False
        
        # 保存结果
        with open("debug_file_result.zip", "wb") as f:
            f.write(response.content)
        print("文件上传成功，结果已保存")
        return True
        
    except Exception as e:
        print(f"文件上传失败: {e}")
        return False
    finally:
        if os.path.exists(temp_file):
            os.remove(temp_file)


def main():
    print("🔍 开始调试上传问题...")
    print("="*50)
    
    # 1. 测试基础连接
    print("1. 测试健康检查...")
    if not test_health():
        print("❌ 服务器连接失败")
        return
    
    # 2. 测试配置
    print("\n2. 测试配置获取...")
    if not test_config():
        print("❌ 配置获取失败")
        return
    
    # 3. 测试JSON分析
    print("\n3. 测试JSON分析...")
    json_success = test_simple_json()
    
    # 4. 测试文件上传
    print("\n4. 测试文件上传...")
    file_success = test_file_upload()
    
    print("\n" + "="*50)
    print("调试结果:")
    print(f"JSON分析: {'✅ 成功' if json_success else '❌ 失败'}")
    print(f"文件上传: {'✅ 成功' if file_success else '❌ 失败'}")
    
    if json_success and file_success:
        print("\n🎉 所有功能正常！前端应该可以正常使用了。")
        print("访问: http://localhost:8001/ui")
    else:
        print("\n⚠️ 仍有问题需要解决")


if __name__ == "__main__":
    main()
