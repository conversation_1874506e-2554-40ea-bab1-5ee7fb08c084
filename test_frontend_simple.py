#!/usr/bin/env python3
"""
简单的前端功能测试
"""

import requests
import json
import tempfile
import os


def test_json_api():
    """测试JSON分析API"""
    print("🧪 测试JSON分析API...")
    
    # 创建足够的测试数据
    test_data = [
        {
            "msgId": "test_001",
            "message": {
                "user_request": "我想查询订单状态",
                "response": {"解释": "正在为您查询订单信息"}
            },
            "context": "{\"user_id\": \"123\"}"
        },
        {
            "msgId": "test_002",
            "message": {
                "user_request": "如何退款",
                "response": {"解释": "为您介绍退款流程"}
            },
            "context": "{\"user_id\": \"456\"}"
        },
        {
            "msgId": "test_003",
            "message": {
                "user_request": "商品质量问题",
                "response": {"解释": "我们会处理质量问题"}
            },
            "context": "{\"user_id\": \"789\"}"
        },
        {
            "msgId": "test_004",
            "message": {
                "user_request": "发货时间查询",
                "response": {"解释": "预计3-5个工作日发货"}
            },
            "context": "{\"user_id\": \"101\"}"
        },
        {
            "msgId": "test_005",
            "message": {
                "user_request": "物流信息查询",
                "response": {"解释": "为您查询物流状态"}
            },
            "context": "{\"user_id\": \"102\"}"
        },
        {
            "msgId": "test_006",
            "message": {
                "user_request": "售后服务咨询",
                "response": {"解释": "提供售后支持"}
            },
            "context": "{\"user_id\": \"103\"}"
        },
        {
            "msgId": "test_007",
            "message": {
                "user_request": "商品推荐",
                "response": {"解释": "为您推荐合适商品"}
            },
            "context": "{\"user_id\": \"104\"}"
        },
        {
            "msgId": "test_008",
            "message": {
                "user_request": "价格咨询",
                "response": {"解释": "为您介绍价格信息"}
            },
            "context": "{\"user_id\": \"105\"}"
        },
        {
            "msgId": "test_009",
            "message": {
                "user_request": "优惠活动",
                "response": {"解释": "当前有优惠活动"}
            },
            "context": "{\"user_id\": \"106\"}"
        },
        {
            "msgId": "test_010",
            "message": {
                "user_request": "会员权益",
                "response": {"解释": "会员享有特殊权益"}
            },
            "context": "{\"user_id\": \"107\"}"
        }
    ]
    
    request_data = {
        "data": test_data,
        "filename": "frontend_test",
        "config": {
            "k_range": [2, 3],  # 发送数组格式
            "max_features": 100,
            "clustering_max_features": 50,
            "compare_k_values": [2]
        }
    }
    
    try:
        print("   发送分析请求...")
        response = requests.post(
            "http://localhost:8001/analyze/json",
            json=request_data,
            timeout=60
        )
        
        print(f"   响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ JSON分析API测试成功")
            
            # 保存结果文件
            result_file = "frontend_api_test_result.zip"
            with open(result_file, 'wb') as f:
                f.write(response.content)
            print(f"   结果已保存到: {result_file}")
            
            return True
        else:
            print(f"❌ JSON分析API测试失败: HTTP {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ JSON分析API测试失败: {e}")
        return False


def test_file_upload_api():
    """测试文件上传API"""
    print("\n📁 测试文件上传API...")
    
    # 创建临时JSON文件
    test_data = [
        {
            "msgId": "file_test_001",
            "message": {
                "user_request": "测试文件上传",
                "response": {"解释": "这是文件上传测试"}
            },
            "context": "{\"user_id\": \"test\"}"
        },
        {
            "msgId": "file_test_002",
            "message": {
                "user_request": "另一个测试消息",
                "response": {"解释": "这是另一个测试"}
            },
            "context": "{\"user_id\": \"test2\"}"
        }
    ]
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
        temp_file_path = f.name
    
    try:
        # 准备文件上传
        with open(temp_file_path, 'rb') as f:
            files = {'file': ('test_data.json', f, 'application/json')}
            data = {
                'config': json.dumps({
                    "k_range": [2, 3],
                    "max_features": 100,
                    "clustering_max_features": 50,
                    "compare_k_values": [2]
                })
            }
            
            print("   发送文件上传请求...")
            response = requests.post(
                "http://localhost:8001/analyze/file",
                files=files,
                data=data,
                timeout=60
            )
        
        print(f"   响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 文件上传API测试成功")
            
            # 保存结果文件
            result_file = "frontend_file_test_result.zip"
            with open(result_file, 'wb') as f:
                f.write(response.content)
            print(f"   结果已保存到: {result_file}")
            
            return True
        else:
            print(f"❌ 文件上传API测试失败: HTTP {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 文件上传API测试失败: {e}")
        return False
    finally:
        # 清理临时文件
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)


def main():
    """主测试函数"""
    print("🚀 开始前端API测试...")
    print("="*50)
    
    results = []
    
    # 测试JSON API
    results.append(("JSON分析API", test_json_api()))
    
    # 测试文件上传API
    results.append(("文件上传API", test_file_upload_api()))
    
    # 输出测试结果
    print("\n" + "="*50)
    print("📊 测试结果汇总")
    print("="*50)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "="*50)
    if all_passed:
        print("🎉 所有API测试通过！")
        print("\n💡 前端可以正常使用以下功能:")
        print("   • JSON数据分析")
        print("   • 文件上传分析")
        print("   • 配置参数传递")
        print("   • 结果文件下载")
        
        print("\n🌐 访问前端页面: http://localhost:8001/ui")
        
    else:
        print("⚠️  部分API测试失败，请检查相关组件")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
