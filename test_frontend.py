#!/usr/bin/env python3
"""
测试前端页面功能
"""

import requests
import json
import time
import webbrowser
from pathlib import Path


def test_frontend_access():
    """测试前端页面访问"""
    print("🌐 测试前端页面访问...")
    
    try:
        response = requests.get("http://localhost:8001/ui", timeout=10)
        if response.status_code == 200:
            print("✅ 前端页面访问成功")
            print(f"   页面大小: {len(response.content)} 字节")
            return True
        else:
            print(f"❌ 前端页面访问失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 前端页面访问失败: {e}")
        return False


def test_static_files():
    """测试静态文件访问"""
    print("\n📁 测试静态文件访问...")
    
    static_files = [
        "/ui/static/css/style.css",
        "/ui/static/js/app.js"
    ]
    
    all_success = True
    for file_path in static_files:
        try:
            response = requests.get(f"http://localhost:8001{file_path}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {file_path} - 访问成功 ({len(response.content)} 字节)")
            else:
                print(f"❌ {file_path} - 访问失败: HTTP {response.status_code}")
                all_success = False
        except Exception as e:
            print(f"❌ {file_path} - 访问失败: {e}")
            all_success = False
    
    return all_success


def test_api_endpoints():
    """测试API端点"""
    print("\n🔌 测试API端点...")
    
    endpoints = [
        ("/health", "健康检查"),
        ("/config/default", "默认配置"),
        ("/docs", "API文档")
    ]
    
    all_success = True
    for endpoint, description in endpoints:
        try:
            response = requests.get(f"http://localhost:8001{endpoint}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {endpoint} - {description} 正常")
            else:
                print(f"❌ {endpoint} - {description} 失败: HTTP {response.status_code}")
                all_success = False
        except Exception as e:
            print(f"❌ {endpoint} - {description} 失败: {e}")
            all_success = False
    
    return all_success


def test_json_analysis_api():
    """测试JSON分析API"""
    print("\n🧪 测试JSON分析API...")
    
    # 创建测试数据
    test_data = [
        {
            "msgId": "test_001",
            "message": {
                "user_request": "我想查询订单状态",
                "response": {"解释": "正在为您查询订单信息"}
            },
            "context": "{\"user_id\": \"123\"}"
        },
        {
            "msgId": "test_002", 
            "message": {
                "user_request": "如何退款",
                "response": {"解释": "为您介绍退款流程"}
            },
            "context": "{\"user_id\": \"456\"}"
        },
        {
            "msgId": "test_003",
            "message": {
                "user_request": "商品质量问题", 
                "response": {"解释": "我们会处理质量问题"}
            },
            "context": "{\"user_id\": \"789\"}"
        }
    ]
    
    request_data = {
        "data": test_data,
        "filename": "frontend_test",
        "config": {
            "k_range": [2, 3],
            "max_features": 100,
            "clustering_max_features": 50,
            "compare_k_values": [2]
        }
    }
    
    try:
        print("   发送分析请求...")
        response = requests.post(
            "http://localhost:8001/analyze/json",
            json=request_data,
            timeout=60
        )
        
        if response.status_code == 200:
            print("✅ JSON分析API测试成功")
            
            # 保存结果文件
            result_file = "frontend_test_result.zip"
            with open(result_file, 'wb') as f:
                f.write(response.content)
            print(f"   结果已保存到: {result_file}")
            
            return True
        else:
            print(f"❌ JSON分析API测试失败: HTTP {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ JSON分析API测试失败: {e}")
        return False


def open_frontend_in_browser():
    """在浏览器中打开前端页面"""
    print("\n🌐 在浏览器中打开前端页面...")
    
    try:
        webbrowser.open("http://localhost:8001/ui")
        print("✅ 已在默认浏览器中打开前端页面")
        print("   URL: http://localhost:8001/ui")
        return True
    except Exception as e:
        print(f"❌ 无法打开浏览器: {e}")
        print("   请手动访问: http://localhost:8001/ui")
        return False


def main():
    """主测试函数"""
    print("🚀 开始前端功能测试...")
    print("="*60)
    
    results = []
    
    # 测试前端页面访问
    results.append(("前端页面访问", test_frontend_access()))
    
    # 测试静态文件
    results.append(("静态文件访问", test_static_files()))
    
    # 测试API端点
    results.append(("API端点", test_api_endpoints()))
    
    # 测试JSON分析API
    results.append(("JSON分析API", test_json_analysis_api()))
    
    # 输出测试结果
    print("\n" + "="*60)
    print("📊 前端测试结果汇总")
    print("="*60)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "="*60)
    if all_passed:
        print("🎉 所有前端测试通过！")
        print("\n💡 前端系统已准备就绪，功能包括:")
        print("   • 文件上传分析")
        print("   • JSON数据分析")
        print("   • 实时进度显示")
        print("   • 结果自动下载")
        print("   • 响应式设计")
        
        # 在浏览器中打开
        print("\n🌐 正在打开前端页面...")
        open_frontend_in_browser()
        
        print("\n📖 使用说明:")
        print("   1. 访问 http://localhost:8001/ui 使用Web界面")
        print("   2. 访问 http://localhost:8001/docs 查看API文档")
        print("   3. 选择文件上传或JSON输入进行分析")
        print("   4. 配置分析参数后开始分析")
        print("   5. 等待分析完成并自动下载结果")
        
    else:
        print("⚠️  部分前端测试失败，请检查相关组件")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
