# 消息分析系统

一个基于K-means聚类和词云分析的消息分析系统，支持命令行和FastAPI Web接口。

## 功能特性

- **K-means聚类分析**: 自动寻找最优聚类数量，对用户消息进行智能分类
- **词云生成**: 提取关键词并生成中文词云图表
- **多种输入格式**: 支持JSON、JSONL、gzip压缩文件
- **Web API接口**: 提供FastAPI服务，支持文件上传和在线分析
- **结果导出**: 自动生成分析报告、可视化图表和Excel文件

## 安装依赖

```bash
# 使用uv安装依赖（推荐）
uv sync

# 或使用pip安装
pip install -e .
```

## 使用方法

### 1. 命令行方式（保持原有使用方式）

```bash
# 基本用法
python run_analysis.py

# 指定数据文件和输出目录
python run_analysis.py --data-path ./data/your_data.json --output ./results

# 自定义参数
python run_analysis.py \
    --data-path ./data/your_data.json \
    --output ./results \
    --k-range 5,30 \
    --max-features 2000 \
    --compare-k 3,5,8,10
```

### 2. FastAPI Web服务

#### 启动服务

```bash
# 启动API服务
python start_api.py

# 自定义端口和主机
python start_api.py --host 0.0.0.0 --port 8080 --reload
```

服务启动后可以访问：
- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health

#### API接口

**上传文件分析**
```bash
curl -X POST "http://localhost:8000/analyze/file" \
     -F "file=@your_data.json" \
     -F "config={\"max_features\": 1000}"
```

**JSON数据分析**
```bash
curl -X POST "http://localhost:8000/analyze/json" \
     -H "Content-Type: application/json" \
     -d '{
       "data": [...],
       "filename": "my_data",
       "config": {"max_features": 1000}
     }'
```

### 3. Python客户端

```python
from examples.api_client import MessageAnalysisClient

# 创建客户端
client = MessageAnalysisClient("http://localhost:8000")

# 分析文件
result_path = client.analyze_file("data.json")

# 分析JSON数据
data = [{"message": {"user_request": "..."}, ...}]
result_path = client.analyze_json_data(data, "my_analysis")
```

## 支持的文件格式

### 输入格式

1. **JSON文件** (`.json`)
   ```json
   [
     {
       "msgId": "msg_001",
       "message": {
         "user_request": "用户消息内容",
         "response": {"解释": "回复内容"}
       },
       "context": "{\"user_id\": \"123\"}"
     }
   ]
   ```

2. **JSON Lines文件** (`.jsonl`)
   ```
   {"msgId": "msg_001", "message": {...}}
   {"msgId": "msg_002", "message": {...}}
   ```

3. **Gzip压缩文件** (`.gz`)
   - 支持压缩的JSON或JSONL文件

### 输出格式

分析完成后会生成包含以下文件的zip包：

- `user_request_clusters_k{N}.png` - 聚类可视化图表
- `user_request_clustering_results_k{N}.csv` - 详细聚类结果
- `cluster_summary_k{N}.txt` - 聚类摘要报告
- `chinese_wordcloud_quality.xlsx` - 词云数据Excel文件
- `chinese_wordcloud_quality.png` - 词云图片

## 配置选项

```json
{
  "k_range": [10, 50],           // K值搜索范围
  "max_features": 3000,          // TF-IDF最大特征数
  "clustering_max_features": 1000, // 聚类时的最大特征数
  "random_state": 42,            // 随机种子
  "compare_k_values": [3, 5, 8], // 对比分析的K值
  "min_text_length": 3,          // 最小文本长度
  "wordcloud_params": {          // 词云参数
    "max_words": 50,
    "width": 400,
    "height": 200
  }
}
```

## 项目结构

```
message-analysis/
├── core/                    # 核心分析模块
│   └── message_analyzer.py # 主要分析类
├── api/                     # FastAPI接口
│   └── main.py             # API主文件
├── utils/                   # 工具模块
│   ├── load_json.py        # JSON加载
│   ├── clean_log.py        # 数据清理
│   ├── kmeans_clustering.py # K-means聚类
│   ├── create_wordcloud.py # 词云生成
│   └── ...
├── examples/               # 示例代码
│   └── api_client.py      # API客户端示例
├── piplines/              # 原有管道（保持兼容）
├── run_analysis.py        # 命令行入口
├── start_api.py          # API服务启动脚本
└── README.md             # 说明文档
```

## 代码优化说明

### 重构内容

1. **核心类封装**: 创建`MessageAnalyzer`类，整合所有分析功能
2. **配置管理**: 统一的配置系统，支持自定义参数
3. **错误处理**: 完善的异常处理和错误信息
4. **代码复用**: 消除重复代码，提高可维护性
5. **接口标准化**: 统一的输入输出接口

### 新增功能

1. **FastAPI支持**: 完整的Web API接口
2. **多格式支持**: gzip、JSON、JSONL文件支持
3. **在线分析**: 支持直接上传数据进行分析
4. **结果打包**: 自动创建zip结果包
5. **客户端库**: 提供Python客户端示例

### 保持兼容性

- 原有的命令行使用方式完全保持不变
- 所有原有功能都得到保留
- 输出格式和文件结构保持一致

## 开发和测试

```bash
# 运行命令行版本
python run_analysis.py --data-path ./data/807凌晨改写log.json

# 启动API服务（开发模式）
python start_api.py --reload

# 运行客户端示例
python examples/api_client.py
```

## 注意事项

1. 确保数据文件格式正确，包含必要的字段
2. 大文件分析可能需要较长时间，请耐心等待
3. API服务默认端口8000，确保端口未被占用
4. 生成的临时文件会自动清理
