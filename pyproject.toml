[project]
name = "message-analysis"
version = "0.1.0"
description = "Message analysis system with K-means clustering and FastAPI support"
readme = "README.md"
requires-python = ">=3.13"
authors = [
    { name = "“wanlige”", email = "<EMAIL>" }
]
dependencies = [
    "fastjsonschema>=2.21.1",
    "ijson>=3.4.0",
    "jieba>=0.42.1",
    "json-repair>=0.48.0",
    "matplotlib>=3.10.5",
    "numpy>=2.3.2",
    "pandas>=2.3.1",
    "ruff>=0.12.7",
    "scikit-learn>=1.7.1",
    "seaborn>=0.13.2",
    "tqdm>=4.67.1",
    "wordcloud==1.9.4",
    "openpyxl==3.1.5",
    "et-xmlfile==2.0.0",
    "fastapi>=0.104.0",
    "uvicorn>=0.24.0",
    "python-multipart>=0.0.6",
    "aiofiles>=23.2.0",
    "requests>=2.31.0"
]