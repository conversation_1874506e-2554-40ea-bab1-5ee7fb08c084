# 消息分析系统前端使用指南

## 🌐 访问地址

**前端界面**: http://localhost:8001/ui  
**API文档**: http://localhost:8001/docs

## 🚀 功能概览

消息分析系统前端提供了直观的Web界面，支持两种分析方式：

### 1. 📁 文件上传分析
- 支持 `.json`, `.jsonl`, `.gz` 格式文件
- 自动解析文件内容
- 适合批量数据分析

### 2. 📝 JSON数据分析  
- 直接在网页中输入JSON数据
- 实时验证数据格式
- 适合小量数据快速分析

## 📊 使用步骤

### 方式一：文件上传分析

1. **选择功能**
   - 点击"文件上传分析"卡片
   - 或点击"选择文件"按钮

2. **上传文件**
   - 点击文件选择框选择文件
   - 或直接拖拽文件到选择区域
   - 支持格式：`.json`, `.jsonl`, `.gz`

3. **配置参数**
   - **K值范围**: 聚类数量范围，如 `5,15`
   - **最大特征数**: TF-IDF特征数量，建议 `1000`
   - **聚类特征数**: 聚类使用的特征数，建议 `500`
   - **对比K值**: 用于对比的K值，如 `5,8,10`

4. **开始分析**
   - 点击"开始分析"按钮
   - 等待分析完成
   - 结果会自动下载为ZIP文件

### 方式二：JSON数据分析

1. **选择功能**
   - 点击"JSON数据分析"卡片
   - 或点击"输入数据"按钮

2. **输入数据**
   - 在文本框中输入JSON格式的数据
   - 数据格式示例：
   ```json
   [
     {
       "msgId": "msg_001",
       "message": {
         "user_request": "我想查询订单状态",
         "response": {"解释": "正在为您查询订单信息"}
       },
       "context": "{\"user_id\": \"123\"}"
     }
   ]
   ```

3. **设置文件名**
   - 输入结果文件名（不含扩展名）
   - 默认为 `json_analysis`

4. **配置参数**
   - 参数设置与文件上传相同
   - 建议小数据使用较小的K值范围

5. **开始分析**
   - 点击"开始分析"按钮
   - 等待分析完成
   - 结果会自动下载为ZIP文件

## ⚙️ 参数说明

### K值范围 (k_range)
- **作用**: 确定聚类数量的搜索范围
- **格式**: `起始值,结束值`，如 `5,15`
- **建议**: 
  - 小数据集（<100条）：`2,8`
  - 中等数据集（100-1000条）：`5,15`
  - 大数据集（>1000条）：`10,30`

### 最大特征数 (max_features)
- **作用**: TF-IDF向量化的最大特征数量
- **建议**:
  - 小数据集：`100-500`
  - 中等数据集：`500-1500`
  - 大数据集：`1000-3000`

### 聚类特征数 (clustering_max_features)
- **作用**: 实际用于聚类的特征数量
- **建议**: 通常为最大特征数的1/2到2/3

### 对比K值 (compare_k_values)
- **作用**: 生成多个K值的对比结果
- **格式**: 用逗号分隔，如 `3,5,8`
- **建议**: 选择3-5个代表性的K值

## 📦 结果文件说明

分析完成后会下载一个ZIP压缩包，包含以下文件：

### 核心结果文件
- `user_request_clustering_results_k{N}.csv` - 聚类结果数据
- `cluster_summary_k{N}.txt` - 聚类摘要报告
- `user_request_clusters_k{N}.png` - 聚类可视化图

### 分析报告
- `optimal_k_analysis.png` - 最优K值分析图
- `chinese_wordcloud_quality.png` - 词云图
- `chinese_wordcloud_quality.xlsx` - 词频分析表

### 对比文件
- 多个K值的对比结果文件
- 不同参数设置的分析结果

## 🔧 故障排除

### 常见问题

1. **文件上传失败**
   - 检查文件格式是否正确
   - 确保文件大小不超过100MB
   - 验证JSON格式是否有效

2. **分析失败**
   - 确保数据量足够（建议至少10条）
   - 检查数据格式是否符合要求
   - 调整参数设置（降低特征数）

3. **页面无法访问**
   - 确认API服务器正在运行
   - 检查端口8001是否被占用
   - 尝试刷新页面

4. **结果下载失败**
   - 检查浏览器下载设置
   - 确保有足够的磁盘空间
   - 尝试使用其他浏览器

### 数据格式要求

**必需字段**:
- `msgId`: 消息唯一标识
- `message.user_request`: 用户请求内容
- `message.response`: 系统回复内容

**可选字段**:
- `context`: 上下文信息
- 其他自定义字段

**示例数据**:
```json
{
  "msgId": "unique_id",
  "message": {
    "user_request": "用户的问题或请求",
    "response": {
      "解释": "系统的回复内容"
    }
  },
  "context": "{\"additional\": \"info\"}"
}
```

## 💡 使用技巧

1. **数据预处理**
   - 确保文本内容有意义且长度适中
   - 移除过短或过长的消息
   - 统一数据格式

2. **参数调优**
   - 从小范围开始测试
   - 根据数据特点调整参数
   - 观察聚类效果进行优化

3. **结果分析**
   - 重点关注聚类摘要文件
   - 结合词云图理解主题
   - 使用可视化图表分析分布

4. **性能优化**
   - 大文件建议分批处理
   - 合理设置特征数量
   - 避免过大的K值范围

## 🎯 最佳实践

1. **数据质量**: 确保数据完整性和一致性
2. **参数设置**: 根据数据规模合理配置
3. **结果验证**: 人工验证聚类结果的合理性
4. **迭代优化**: 根据结果调整参数重新分析

---

**技术支持**: 如遇问题请查看API文档或联系技术团队
