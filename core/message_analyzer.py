"""
核心消息分析器类
整合所有分析功能，提供简洁的API接口
"""

import os
import sys
import json
import gzip
import tempfile
import zipfile
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Union
from pathlib import Path

import pandas as pd

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

from utils import (
    load_json,
    spilit_log_message,
    clean_log,
    jieba_optimized_common_substring,
    create_wordcloud
)
from utils.kmeans_clustering import (
    perform_kmeans_clustering,
    visualize_clusters,
    find_optimal_k
)


class MessageAnalyzer:
    """消息分析器核心类"""
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化分析器

        Args:
            config: 配置字典，包含各种参数设置
        """
        self.config = self._get_default_config()
        if config:
            # 处理配置中的特殊格式
            processed_config = self._process_config(config)
            self.config.update(processed_config)

    def _process_config(self, config: Dict) -> Dict:
        """
        处理配置中的特殊格式

        Args:
            config: 原始配置字典

        Returns:
            Dict: 处理后的配置字典
        """
        processed = config.copy()

        # 处理k_range：如果是列表，转换为range对象
        if 'k_range' in processed and isinstance(processed['k_range'], list):
            k_list = processed['k_range']
            if len(k_list) >= 2:
                # 假设列表格式为 [start, end]
                processed['k_range'] = range(k_list[0], k_list[1] + 1)
            elif len(k_list) == 1:
                # 如果只有一个值，创建一个小范围
                processed['k_range'] = range(max(2, k_list[0] - 1), k_list[0] + 2)

        return processed

    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'k_range': range(10, 50),
            'max_features': 3000,
            'clustering_max_features': 1000,
            'random_state': 42,
            'compare_k_values': [3, 5, 8],
            'min_text_length': 3,
            'wordcloud_params': {
                'max_words': 50,
                'width': 400,
                'height': 200
            }
        }
    
    def load_data(self, data_source: Union[str, List[Dict], bytes]) -> Tuple[List[Dict], List[Dict]]:
        """
        加载数据，支持多种输入格式
        
        Args:
            data_source: 数据源，可以是文件路径、数据列表或gzip压缩的字节数据
            
        Returns:
            Tuple[List[Dict], List[Dict]]: (有效日志数据, 错误数据)
        """
        if isinstance(data_source, str):
            # 文件路径
            data = load_json.load_jsonl(data_source)
        elif isinstance(data_source, bytes):
            # gzip压缩的字节数据
            with gzip.decompress(data_source) as decompressed:
                data = json.loads(decompressed.decode('utf-8'))
        elif isinstance(data_source, list):
            # 直接的数据列表
            data = data_source
        else:
            raise ValueError("不支持的数据源类型")
        
        print(f"成功加载数据: {len(data)} 条原始数据")

        # 检查数据是否已经是处理后的格式
        if self._is_processed_data(data):
            print("检测到已处理的数据格式，跳过分割步骤")
            # 直接清理数据
            clean_data = clean_log.clean_log(data)
            error_data = []
        else:
            print("检测到原始日志格式，执行分割处理")
            # 分割和清理数据
            log_data, error_data = spilit_log_message.split_log_pre_message(data)
            clean_data = clean_log.clean_log(log_data)

        print(f"有效日志数据: {len(clean_data)} 条")
        print(f"错误数据: {len(error_data)} 条")

        return clean_data, error_data

    def _is_processed_data(self, data: List[Dict]) -> bool:
        """
        检查数据是否已经是处理后的格式

        Args:
            data: 数据列表

        Returns:
            bool: True表示已处理，False表示原始格式
        """
        if not data or len(data) == 0:
            return False

        # 检查第一个数据项的结构
        first_item = data[0]

        # 如果message是字典且包含user_request，认为是已处理的数据
        if (isinstance(first_item.get('message'), dict) and
            'user_request' in first_item['message']):
            return True

        # 如果message是字符串，认为是原始日志格式
        if isinstance(first_item.get('message'), str):
            return False

        # 默认认为是已处理的数据
        return True

    def analyze(self, data_source: Union[str, List[Dict], bytes],
                output_dir: Optional[str] = None) -> Dict:
        """
        执行完整的分析流程
        
        Args:
            data_source: 数据源
            output_dir: 输出目录，如果为None则创建临时目录
            
        Returns:
            Dict: 分析结果字典
        """
        # 创建输出目录
        if output_dir is None:
            output_dir = self._create_output_directory()
        else:
            os.makedirs(output_dir, exist_ok=True)
        
        # 加载数据
        log_data, error_data = self.load_data(data_source)
        
        if len(log_data) == 0:
            raise ValueError("没有有效的日志数据")
        
        # 准备聚类数据
        prepared_data = self._prepare_clustering_data(log_data)
        
        if len(prepared_data) == 0:
            raise ValueError("没有有效的用户请求数据")
        
        print(f"准备用于聚类的数据: {len(prepared_data)} 条")
        
        # 执行分析
        results = {
            'output_dir': output_dir,
            'data_stats': {
                'total_raw': len(log_data) + len(error_data),
                'valid_logs': len(log_data),
                'error_logs': len(error_data),
                'clustering_data': len(prepared_data)
            }
        }
        
        # 1. 寻找最优K值
        print("\n" + "="*60)
        print("第一步：寻找最优聚类数量")
        print("="*60)
        
        try:
            evaluation_results, best_k = find_optimal_k(
                prepared_data, 
                k_range=self.config['k_range'], 
                max_features=self.config['max_features']
            )
            results['optimal_k'] = best_k
            results['k_evaluation'] = evaluation_results
        except Exception as e:
            print(f"寻找最优k值时出错: {e}")
            best_k = 5
            results['optimal_k'] = best_k
            results['k_evaluation'] = None
        
        # 2. 执行聚类分析
        print(f"\n第二步：使用最优k值({best_k})进行详细聚类分析")
        clustering_results = self._perform_clustering_analysis(
            prepared_data, log_data, best_k, output_dir
        )
        results['clustering'] = clustering_results
        
        # 3. 对比分析
        print(f"\n第三步：对比分析不同k值的聚类效果")
        comparison_results = self._perform_comparison_analysis(
            prepared_data, best_k, output_dir
        )
        results['comparison'] = comparison_results
        
        # 4. 关键词分析和词云
        print(f"\n第四步：关键词分析和词云生成")
        wordcloud_results = self._perform_wordcloud_analysis(log_data, output_dir)
        results['wordcloud'] = wordcloud_results
        
        print("\n" + "="*60)
        print("分析完成！")
        print("="*60)
        print(f"所有结果已保存到目录: {output_dir}")
        
        return results
    
    def _create_output_directory(self, base_name: str = "analysis_results") -> str:
        """创建输出目录"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = f"{base_name}_{timestamp}"
        os.makedirs(output_dir, exist_ok=True)
        return output_dir
    
    def _prepare_clustering_data(self, log_data: List[Dict]) -> List[Dict]:
        """准备聚类数据"""
        prepared_data = []
        for i, item in enumerate(log_data):
            try:
                # 尝试从不同的数据结构中提取用户请求
                user_request = None

                # 方式1: 标准格式 item['message']['user_request']
                if isinstance(item.get('message'), dict) and 'user_request' in item['message']:
                    user_request = item['message']['user_request']

                # 方式2: 直接在item中
                elif 'user_request' in item:
                    user_request = item['user_request']

                # 方式3: 从原始message中解析（兼容原有数据格式）
                elif isinstance(item.get('message'), str):
                    # 这里可以添加更复杂的解析逻辑
                    pass

                if user_request and isinstance(user_request, str) and len(user_request.strip()) >= self.config['min_text_length']:
                    prepared_data.append({
                        'user_request': user_request.strip(),
                        'original_index': i,
                        'msgId': item.get('msgId', f'msg_{i}'),
                        'message':{
                    'user_request': user_request.strip()
                },
                    })
            except (KeyError, TypeError) as e:
                print(f"准备数据时索引 {i} 处理失败: {e}")
                continue
        return prepared_data
    
    def _perform_clustering_analysis(self, prepared_data: List[Dict], 
                                   log_data: List[Dict], k: int, 
                                   output_dir: str) -> Dict:
        """执行聚类分析"""
        clustering_results = perform_kmeans_clustering(
            prepared_data, 
            n_clusters=k, 
            max_features=self.config['clustering_max_features'],
            random_state=self.config['random_state']
        )
        
        # 添加msgId映射
        results_df = clustering_results['results_df']
        clustering_results['results_df'] = self._add_msgid_mapping(results_df, log_data)
        
        # 保存结果
        self._save_clustering_results(clustering_results, k, output_dir)
        
        # 生成可视化
        try:
            visualization_path = os.path.join(output_dir, f'user_request_clusters_k{k}.png')
            visualize_clusters(clustering_results, save_path=visualization_path)
            clustering_results['visualization_path'] = visualization_path
        except Exception as e:
            print(f"可视化时出错: {e}")
            clustering_results['visualization_path'] = None
        
        return clustering_results
    
    def _perform_comparison_analysis(self, prepared_data: List[Dict], 
                                   best_k: int, output_dir: str) -> Dict:
        """执行对比分析"""
        compare_k_values = self.config['compare_k_values'].copy()
        if best_k not in compare_k_values:
            compare_k_values.append(best_k)
        
        comparison_results = {}
        
        for k in sorted(set(compare_k_values)):
            if k == best_k:
                continue
                
            print(f"\n分析 k={k} 的聚类效果...")
            try:
                results = perform_kmeans_clustering(
                    prepared_data, 
                    n_clusters=k, 
                    max_features=self.config['clustering_max_features'],
                    random_state=self.config['random_state']
                )
                
                # 保存结果
                csv_path = os.path.join(output_dir, f'user_request_clustering_results_k{k}.csv')
                results['results_df'].to_csv(csv_path, index=False, encoding='utf-8-sig')
                
                comparison_results[k] = {
                    'silhouette_score': results['silhouette_score'],
                    'data_count': len(results['results_df']),
                    'csv_path': csv_path
                }
                
                print(f"k={k}, 轮廓系数: {results['silhouette_score']:.4f}")
                
            except Exception as e:
                print(f"分析 k={k} 时出错: {e}")
                comparison_results[k] = {'error': str(e)}
        
        return comparison_results
    
    def _perform_wordcloud_analysis(self, log_data: List[Dict], output_dir: str) -> Dict:
        """执行词云分析"""
        try:
            enhanced_data, word_frequency = jieba_optimized_common_substring.extract_keywords_from_pre_user_data(log_data)
            
            excel_path = os.path.join(output_dir, 'chinese_wordcloud_quality.xlsx')
            png_path = os.path.join(output_dir, 'chinese_wordcloud_quality.png')
            
            result = create_wordcloud.create_chinese_wordcloud_excel_quality(
                word_frequency,
                filename=excel_path,
                output_dir=output_dir
            )
            
            return {
                'excel_path': excel_path,
                'png_path': png_path if os.path.exists(png_path) else None,
                'word_count': len(word_frequency),
                'result_message': result
            }
        except Exception as e:
            print(f"词云分析时出错: {e}")
            return {'error': str(e)}
    
    def _add_msgid_mapping(self, results_df: pd.DataFrame, log_data: List[Dict]) -> pd.DataFrame:
        """添加msgId映射"""
        # 这里简化实现，实际应该根据原始索引映射
        if 'msgId' not in results_df.columns:
            results_df['msgId'] = [f'msg_{i}' for i in range(len(results_df))]
        return results_df
    
    def _save_clustering_results(self, clustering_results: Dict, k: int, output_dir: str):
        """保存聚类结果"""
        # 保存CSV
        csv_path = os.path.join(output_dir, f'user_request_clustering_results_k{k}.csv')
        clustering_results['results_df'].to_csv(csv_path, index=False, encoding='utf-8-sig')
        
        # 保存摘要
        summary_path = os.path.join(output_dir, f'cluster_summary_k{k}.txt')
        self._save_cluster_summary(clustering_results, summary_path)
    
    def _save_cluster_summary(self, clustering_results: Dict, filename: str):
        """保存聚类摘要"""
        cluster_info = clustering_results['cluster_info']
        silhouette_score_val = clustering_results['silhouette_score']
        n_clusters = clustering_results['n_clusters']
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"K-means聚类分析结果摘要\n")
            f.write(f"{'='*50}\n\n")
            f.write(f"聚类数量: {n_clusters}\n")
            f.write(f"轮廓系数: {silhouette_score_val:.4f}\n")
            f.write(f"数据总量: {len(clustering_results['results_df'])}\n\n")
            
            for cluster_id in range(n_clusters):
                info = cluster_info[cluster_id]
                f.write(f"\n【聚类 {cluster_id}】\n")
                f.write(f"数据量: {info['size']} ({info['percentage']:.1f}%)\n")
                f.write(f"主要特征词:\n")
                for feature, score in info['top_features'][:5]:
                    f.write(f"  - {feature}: {score:.4f}\n")
                f.write(f"样本文本:\n")
                for i, text in enumerate(info['sample_texts'][:3], 1):
                    f.write(f"  {i}. {text[:100]}{'...' if len(text) > 100 else ''}\n")
                f.write("\n")
    
    def create_result_zip(self, output_dir: str, zip_filename: Optional[str] = None) -> str:
        """创建结果文件的zip包"""
        if zip_filename is None:
            zip_filename = f"{os.path.basename(output_dir)}.zip"
        
        zip_path = os.path.join(os.path.dirname(output_dir), zip_filename)
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(output_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, output_dir)
                    zipf.write(file_path, arcname)
        
        return zip_path
