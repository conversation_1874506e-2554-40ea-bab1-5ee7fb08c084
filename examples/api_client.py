#!/usr/bin/env python3
"""
API客户端示例
演示如何使用FastAPI接口进行消息分析
"""

import json
import gzip
import requests
from pathlib import Path
from typing import Dict, Any, Optional


class MessageAnalysisClient:
    """消息分析API客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8001"):
        """
        初始化客户端
        
        Args:
            base_url: API服务器地址
        """
        self.base_url = base_url.rstrip('/')
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        response = requests.get(f"{self.base_url}/health")
        response.raise_for_status()
        return response.json()
    
    def get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        response = requests.get(f"{self.base_url}/config/default")
        response.raise_for_status()
        return response.json()
    
    def validate_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """验证配置"""
        response = requests.post(f"{self.base_url}/config/validate", json=config)
        response.raise_for_status()
        return response.json()
    
    def analyze_file(self, file_path: str, config: Optional[Dict[str, Any]] = None, 
                    output_path: Optional[str] = None) -> str:
        """
        分析文件
        
        Args:
            file_path: 要分析的文件路径
            config: 可选配置
            output_path: 输出文件路径，如果为None则使用默认名称
            
        Returns:
            str: 保存的结果文件路径
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 准备请求数据
        files = {'file': (file_path.name, open(file_path, 'rb'))}
        data = {}
        
        if config:
            data['config'] = json.dumps(config)
        
        try:
            # 发送请求
            response = requests.post(
                f"{self.base_url}/analyze/file",
                files=files,
                data=data
            )
            response.raise_for_status()
            
            # 保存结果文件
            if output_path is None:
                output_path = f"analysis_results_{file_path.stem}.zip"
            
            with open(output_path, 'wb') as f:
                f.write(response.content)
            
            return output_path
            
        finally:
            files['file'][1].close()
    
    def analyze_json_data(self, data: list, filename: str = "json_data",
                         config: Optional[Dict[str, Any]] = None,
                         output_path: Optional[str] = None) -> str:
        """
        分析JSON数据
        
        Args:
            data: 要分析的数据列表
            filename: 数据文件名（用于结果文件命名）
            config: 可选配置
            output_path: 输出文件路径，如果为None则使用默认名称
            
        Returns:
            str: 保存的结果文件路径
        """
        # 准备请求数据
        request_data = {
            "data": data,
            "filename": filename
        }
        
        if config:
            request_data["config"] = config
        
        # 发送请求
        response = requests.post(
            f"{self.base_url}/analyze/json",
            json=request_data
        )
        response.raise_for_status()
        
        # 保存结果文件
        if output_path is None:
            output_path = f"analysis_results_{filename}.zip"
        
        with open(output_path, 'wb') as f:
            f.write(response.content)
        
        return output_path


def create_gzip_file(data: list, output_path: str):
    """创建gzip压缩的JSON文件"""
    json_str = json.dumps(data, ensure_ascii=False, indent=2)
    
    with gzip.open(output_path, 'wt', encoding='utf-8') as f:
        f.write(json_str)


def main():
    """示例用法"""
    # 创建客户端
    client = MessageAnalysisClient()
    
    try:
        # 健康检查
        print("检查服务状态...")
        health = client.health_check()
        print(f"服务状态: {health}")
        
        # 获取默认配置
        print("\n获取默认配置...")
        default_config = client.get_default_config()
        print(f"默认配置: {json.dumps(default_config, indent=2, ensure_ascii=False)}")
        
        # 示例数据
        sample_data = [
            {
                "msgId": "msg_001",
                "message": {
                    "user_request": "我想查询订单状态",
                    "response": {"解释": "正在为您查询订单信息"}
                },
                "context": "{\"user_id\": \"123\"}"
            },
            {
                "msgId": "msg_002", 
                "message": {
                    "user_request": "如何退款",
                    "response": {"解释": "为您介绍退款流程"}
                },
                "context": "{\"user_id\": \"456\"}"
            }
        ]
        
        # 方法1: 直接分析JSON数据
        print("\n方法1: 直接分析JSON数据...")
        try:
            result_path = client.analyze_json_data(
                data=sample_data,
                filename="sample_data",
                config={"max_features": 1000}
            )
            print(f"分析完成，结果保存到: {result_path}")
        except Exception as e:
            print(f"JSON数据分析失败: {e}")
        
        # 方法2: 创建并分析gzip文件
        print("\n方法2: 创建并分析gzip文件...")
        try:
            gzip_path = "sample_data.json.gz"
            create_gzip_file(sample_data, gzip_path)
            print(f"创建gzip文件: {gzip_path}")
            
            result_path = client.analyze_file(
                file_path=gzip_path,
                config={"max_features": 1000}
            )
            print(f"分析完成，结果保存到: {result_path}")
        except Exception as e:
            print(f"gzip文件分析失败: {e}")
        
        # 方法3: 分析普通JSON文件
        print("\n方法3: 创建并分析JSON文件...")
        try:
            json_path = "sample_data.json"
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(sample_data, f, ensure_ascii=False, indent=2)
            print(f"创建JSON文件: {json_path}")
            
            result_path = client.analyze_file(
                file_path=json_path,
                config={"max_features": 1000}
            )
            print(f"分析完成，结果保存到: {result_path}")
        except Exception as e:
            print(f"JSON文件分析失败: {e}")
        
    except requests.exceptions.ConnectionError:
        print("错误: 无法连接到API服务器，请确保服务器正在运行")
        print("启动服务器: python start_api.py")
    except Exception as e:
        print(f"客户端错误: {e}")


if __name__ == "__main__":
    main()
