#!/usr/bin/env python3
"""
系统测试脚本
测试重构后的代码和FastAPI服务功能
"""

import json
import gzip
import tempfile
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import requests
from core.message_analyzer import MessageAnalyzer


def create_test_data():
    """创建测试数据"""
    return [
        {
            "msgId": "msg_001",
            "message": {
                "user_request": "我想查询订单状态",
                "response": {"解释": "正在为您查询订单信息"}
            },
            "context": "{\"user_id\": \"123\"}"
        },
        {
            "msgId": "msg_002",
            "message": {
                "user_request": "如何退款",
                "response": {"解释": "为您介绍退款流程"}
            },
            "context": "{\"user_id\": \"456\"}"
        },
        {
            "msgId": "msg_003",
            "message": {
                "user_request": "商品质量问题",
                "response": {"解释": "我们会处理质量问题"}
            },
            "context": "{\"user_id\": \"789\"}"
        },
        {
            "msgId": "msg_004",
            "message": {
                "user_request": "发货时间",
                "response": {"解释": "预计3-5个工作日发货"}
            },
            "context": "{\"user_id\": \"101\"}"
        },
        {
            "msgId": "msg_005",
            "message": {
                "user_request": "订单查询",
                "response": {"解释": "为您查询订单详情"}
            },
            "context": "{\"user_id\": \"102\"}"
        },
        {
            "msgId": "msg_006",
            "message": {
                "user_request": "物流信息",
                "response": {"解释": "为您查询物流状态"}
            },
            "context": "{\"user_id\": \"103\"}"
        },
        {
            "msgId": "msg_007",
            "message": {
                "user_request": "售后服务",
                "response": {"解释": "提供售后支持"}
            },
            "context": "{\"user_id\": \"104\"}"
        },
        {
            "msgId": "msg_008",
            "message": {
                "user_request": "商品推荐",
                "response": {"解释": "为您推荐合适商品"}
            },
            "context": "{\"user_id\": \"105\"}"
        }
    ]


def test_core_analyzer():
    """测试核心分析器"""
    print("="*60)
    print("测试核心分析器")
    print("="*60)
    
    try:
        # 创建测试数据
        test_data = create_test_data()
        
        # 创建分析器
        analyzer = MessageAnalyzer({
            'k_range': range(2, 5),
            'max_features': 100,
            'clustering_max_features': 50,
            'compare_k_values': [2, 3]
        })
        
        # 执行分析
        results = analyzer.analyze(test_data)
        
        print(f"✓ 分析完成")
        print(f"  输出目录: {results['output_dir']}")
        print(f"  最优K值: {results['optimal_k']}")
        print(f"  数据统计: {results['data_stats']}")
        
        # 创建zip包
        zip_path = analyzer.create_result_zip(results['output_dir'])
        print(f"  zip包: {zip_path}")
        
        return True
        
    except Exception as e:
        print(f"✗ 核心分析器测试失败: {e}")
        return False


def test_api_service():
    """测试API服务"""
    print("\n" + "="*60)
    print("测试API服务")
    print("="*60)
    
    base_url = "http://localhost:8001"
    
    try:
        # 健康检查
        response = requests.get(f"{base_url}/health", timeout=5)
        response.raise_for_status()
        print(f"✓ 健康检查: {response.json()}")
        
        # 获取默认配置
        response = requests.get(f"{base_url}/config/default", timeout=5)
        response.raise_for_status()
        config = response.json()
        print(f"✓ 获取默认配置: {len(config)} 个配置项")
        
        # 测试JSON数据分析
        test_data = create_test_data()
        request_data = {
            "data": test_data,
            "filename": "test_data",
            "config": {
                "k_range": [2, 5],
                "max_features": 100,
                "clustering_max_features": 50,
                "compare_k_values": [2, 3]
            }
        }
        
        print("正在测试JSON数据分析...")
        response = requests.post(
            f"{base_url}/analyze/json",
            json=request_data,
            timeout=60
        )
        response.raise_for_status()
        
        # 保存结果
        result_path = "test_api_result.zip"
        with open(result_path, 'wb') as f:
            f.write(response.content)
        
        print(f"✓ JSON数据分析完成，结果保存到: {result_path}")
        
        # 测试gzip文件上传
        print("正在测试gzip文件上传...")
        
        # 创建gzip文件
        gzip_path = "test_data.json.gz"
        json_str = json.dumps(test_data, ensure_ascii=False, indent=2)
        with gzip.open(gzip_path, 'wt', encoding='utf-8') as f:
            f.write(json_str)
        
        # 上传gzip文件
        with open(gzip_path, 'rb') as f:
            files = {'file': ('test_data.json.gz', f, 'application/gzip')}
            data = {'config': json.dumps({"max_features": 100})}
            
            response = requests.post(
                f"{base_url}/analyze/file",
                files=files,
                data=data,
                timeout=60
            )
            response.raise_for_status()
        
        # 保存结果
        gzip_result_path = "test_gzip_result.zip"
        with open(gzip_result_path, 'wb') as f:
            f.write(response.content)
        
        print(f"✓ gzip文件分析完成，结果保存到: {gzip_result_path}")
        
        # 清理临时文件
        os.remove(gzip_path)
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("✗ 无法连接到API服务器，请确保服务器正在运行")
        print("  启动命令: python start_api.py --port 8001")
        return False
    except Exception as e:
        print(f"✗ API服务测试失败: {e}")
        return False


def test_command_line():
    """测试命令行接口"""
    print("\n" + "="*60)
    print("测试命令行接口")
    print("="*60)
    
    try:
        # 创建测试数据文件
        test_data = create_test_data()
        test_file = "test_data.json"
        
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        print(f"✓ 创建测试数据文件: {test_file}")
        
        # 注意：这里只是验证命令行参数解析，不实际运行分析
        # 因为实际运行需要交互式输入
        import subprocess
        result = subprocess.run([
            sys.executable, "run_analysis.py", "--help"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and "K-means聚类分析" in result.stdout:
            print("✓ 命令行接口可用")
            return True
        else:
            print(f"✗ 命令行接口测试失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ 命令行接口测试失败: {e}")
        return False
    finally:
        # 清理测试文件
        if os.path.exists("test_data.json"):
            os.remove("test_data.json")


def main():
    """主测试函数"""
    print("开始系统测试...")
    
    results = []
    
    # 测试核心分析器
    results.append(("核心分析器", test_core_analyzer()))
    
    # 测试API服务
    results.append(("API服务", test_api_service()))
    
    # 测试命令行接口
    results.append(("命令行接口", test_command_line()))
    
    # 输出测试结果
    print("\n" + "="*60)
    print("测试结果汇总")
    print("="*60)
    
    all_passed = True
    for test_name, passed in results:
        status = "✓ 通过" if passed else "✗ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "="*60)
    if all_passed:
        print("🎉 所有测试通过！系统重构成功！")
        print("\n使用说明:")
        print("1. 命令行方式: python run_analysis.py")
        print("2. API服务: python start_api.py")
        print("3. API文档: http://localhost:8000/docs")
    else:
        print("❌ 部分测试失败，请检查相关组件")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
